package models

import "time"

// CurrentUserCheckingInfo 用户检测信息汇总结构体
// 用于收集10轮截图OCR的完整数据，最终传递给扣子API进行大模型分析
type CurrentUserCheckingInfo struct {
	// 基本用户信息
	UserID         string `json:"userID"`         // 用户唯一ID
	UserName       string `json:"userName"`       // 当前用户名
	SiteName       string `json:"siteName"`       // 检测站点名
	RegistrationNo string `json:"registrationNo"` // 用户报到号
	CheckingTime   string `json:"checkingTime"`   // 检测开始时间
	CompletionTime string `json:"completionTime"` // 10轮完成时间

	// 检测轮次数据
	TotalRounds     int         `json:"totalRounds"`     // 总轮次数（固定为10）
	CompletedRounds int         `json:"completedRounds"` // 已完成轮次数
	RoundsData      []RoundData `json:"roundsData"`      // 每轮的详细数据

	// 汇总统计
	TotalScreenshots int            `json:"totalScreenshots"` // 总截图数（应为20）
	ProcessedOCRs    int            `json:"processedOCRs"`    // 已处理的OCR数量
	DetectedOrgans   map[string]int `json:"detectedOrgans"`   // 检测到的器官统计 {器官名: 出现次数}
}

// RoundData 单轮检测数据结构体
type RoundData struct {
	RoundNumber    int                `json:"roundNumber"`    // 轮次编号 (1-10)
	RoundStartTime string             `json:"roundStartTime"` // 轮次开始时间
	RoundEndTime   string             `json:"roundEndTime"`   // 轮次结束时间
	B02Data        *ScreenshotOCRData `json:"b02Data"`        // B02模式截图OCR数据
	C03Data        *ScreenshotOCRData `json:"c03Data"`        // C03模式截图OCR数据
	RoundCompleted bool               `json:"roundCompleted"` // 轮次是否完成
}

// ScreenshotOCRData 单次截图OCR数据结构体
type ScreenshotOCRData struct {
	// 截图基本信息
	ScreenshotMode string `json:"screenshotMode"` // 截图模式 (B02/C03)
	ScreenshotTime string `json:"screenshotTime"` // 截图时间
	ImagePath      string `json:"imagePath"`      // 图片文件路径
	ImageFileName  string `json:"imageFileName"`  // 图片文件名

	// OCR识别结果
	DetectedOrgan string  `json:"detectedOrgan"` // 识别的器官名称
	FullOCRText   string  `json:"fullOCRText"`   // 完整OCR文本
	OCRConfidence float64 `json:"ocrConfidence"` // OCR置信度

	// D值健康趋势数据
	DHealthTrend map[string]string `json:"dHealthTrend"` // D值列表数据 {"数值 (D值)": "器官名称"}
	DValueCount  int               `json:"dValueCount"`  // D值数量

	// 原始OCR JSON数据（用于扣子API分析）
	RawOCRResponse   interface{} `json:"rawOCRResponse"`         // 原始OCR API响应JSON
	ProcessingStatus string      `json:"processingStatus"`       // 处理状态 (success/failed/pending)
	ErrorMessage     string      `json:"errorMessage,omitempty"` // 错误信息（如果有）
}

// NewCurrentUserCheckingInfo 创建新的用户检测信息实例
func NewCurrentUserCheckingInfo(userID, userName, siteName, registrationNo string) *CurrentUserCheckingInfo {
	return &CurrentUserCheckingInfo{
		UserID:           userID,
		UserName:         userName,
		SiteName:         siteName,
		RegistrationNo:   registrationNo,
		CheckingTime:     time.Now().Format("2006-01-02 15:04:05"),
		TotalRounds:      10,
		CompletedRounds:  0,
		RoundsData:       make([]RoundData, 0, 10),
		TotalScreenshots: 0,
		ProcessedOCRs:    0,
		DetectedOrgans:   make(map[string]int),
	}
}

// AddRoundData 添加轮次数据
func (info *CurrentUserCheckingInfo) AddRoundData(roundData RoundData) {
	info.RoundsData = append(info.RoundsData, roundData)
	info.CompletedRounds = len(info.RoundsData)

	// 更新统计信息
	if roundData.B02Data != nil {
		info.TotalScreenshots++
		info.ProcessedOCRs++
		if roundData.B02Data.DetectedOrgan != "" && roundData.B02Data.DetectedOrgan != "未知器官" {
			info.DetectedOrgans[roundData.B02Data.DetectedOrgan]++
		}
	}

	if roundData.C03Data != nil {
		info.TotalScreenshots++
		info.ProcessedOCRs++
		if roundData.C03Data.DetectedOrgan != "" && roundData.C03Data.DetectedOrgan != "未知器官" {
			info.DetectedOrgans[roundData.C03Data.DetectedOrgan]++
		}
	}
}

// IsCompleted 检查是否完成所有10轮检测
func (info *CurrentUserCheckingInfo) IsCompleted() bool {
	return info.CompletedRounds >= 10
}

// MarkCompleted 标记检测完成
func (info *CurrentUserCheckingInfo) MarkCompleted() {
	info.CompletionTime = time.Now().Format("2006-01-02 15:04:05")
}
