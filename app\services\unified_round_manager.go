package services

import (
	"context"
	"fmt"
	"sync"
	"sync/atomic"
	"time"

	"MagneticOperator/app/models"
	"MagneticOperator/app/utils"
	"go.uber.org/zap"
)

// RoundStatusType 轮次状态类型
type RoundStatusType int32

const (
	RoundStatusPending    RoundStatusType = iota // 等待中
	RoundStatusInProgress                        // 进行中
	RoundStatusCompleted                         // 已完成
	RoundStatusError                             // 错误状态
	RoundStatusCancelled                         // 已取消
)

// String 返回状态的字符串表示
func (r RoundStatusType) String() string {
	switch r {
	case RoundStatusPending:
		return "pending"
	case RoundStatusInProgress:
		return "in_progress"
	case RoundStatusCompleted:
		return "completed"
	case RoundStatusError:
		return "error"
	case RoundStatusCancelled:
		return "cancelled"
	default:
		return "unknown"
	}
}

// RoundEventType 轮次事件类型
type RoundEventType string

const (
	EventRoundStarted   RoundEventType = "round_started"
	EventModeCompleted  RoundEventType = "mode_completed"
	EventRoundCompleted RoundEventType = "round_completed"
	EventRoundError     RoundEventType = "round_error"
	EventProgressUpdate RoundEventType = "progress_update"
	EventDataSync       RoundEventType = "data_sync"
)

// RoundEvent 轮次事件
type RoundEvent struct {
	Type      RoundEventType `json:"type"`
	UserKey   string         `json:"user_key"`
	Round     int            `json:"round"`
	Mode      string         `json:"mode"`
	Data      interface{}    `json:"data"`
	Timestamp time.Time      `json:"timestamp"`
}

// EventHandler 事件处理器
type EventHandler func(RoundEvent) error

// UnifiedRoundStatus 统一轮次状态结构体
type UnifiedRoundStatus struct {
	// 基本信息
	UserKey  string `json:"user_key"`  // 用户唯一标识 (用户名_日期)
	UserName string `json:"user_name"` // 用户名

	// 轮次信息
	currentRound int32 `json:"current_round"` // 当前轮次 (1-10) - 使用原子操作

	// 模式完成状态 - 使用原子操作
	b02Completed int32 `json:"b02_completed"` // B02模式是否已完成 (0/1)
	c03Completed int32 `json:"c03_completed"` // C03模式是否已完成 (0/1)

	// 时间信息
	StartTime      time.Time  `json:"start_time"`       // 轮次开始时间
	LastUpdateTime time.Time  `json:"last_update_time"` // 最后更新时间
	CompletionTime *time.Time `json:"completion_time,omitempty"` // 轮次完成时间

	// 数据引用
	DataRef       *models.CurrentUserCheckingInfo `json:"-"` // 数据层引用
	ScreenshotRef *ScreenshotRound                `json:"-"` // 截图管理引用

	// 状态管理 - 使用原子操作
	status     int32 `json:"status"`      // 轮次状态
	errorCount int32 `json:"error_count"` // 错误计数
	retryCount int32 `json:"retry_count"` // 重试计数

	// 并发保护
	mu sync.RWMutex `json:"-"` // 读写锁
}

// NewUnifiedRoundStatus 创建新的统一轮次状态
func NewUnifiedRoundStatus(userKey, userName string) *UnifiedRoundStatus {
	now := time.Now()
	return &UnifiedRoundStatus{
		UserKey:        userKey,
		UserName:       userName,
		currentRound:   1,
		b02Completed:   0,
		c03Completed:   0,
		StartTime:      now,
		LastUpdateTime: now,
		status:         int32(RoundStatusPending),
		errorCount:     0,
		retryCount:     0,
	}
}

// GetCurrentRound 获取当前轮次（原子操作）
func (urs *UnifiedRoundStatus) GetCurrentRound() int {
	return int(atomic.LoadInt32(&urs.currentRound))
}

// SetCurrentRound 设置当前轮次（原子操作）
func (urs *UnifiedRoundStatus) SetCurrentRound(round int) {
	atomic.StoreInt32(&urs.currentRound, int32(round))
	urs.mu.Lock()
	urs.LastUpdateTime = time.Now()
	urs.mu.Unlock()
}

// IsB02Completed 检查B02是否完成（原子操作）
func (urs *UnifiedRoundStatus) IsB02Completed() bool {
	return atomic.LoadInt32(&urs.b02Completed) == 1
}

// SetB02Completed 设置B02完成状态（原子操作）
func (urs *UnifiedRoundStatus) SetB02Completed(completed bool) {
	var value int32
	if completed {
		value = 1
	}
	atomic.StoreInt32(&urs.b02Completed, value)
	urs.mu.Lock()
	urs.LastUpdateTime = time.Now()
	urs.mu.Unlock()
}

// IsC03Completed 检查C03是否完成（原子操作）
func (urs *UnifiedRoundStatus) IsC03Completed() bool {
	return atomic.LoadInt32(&urs.c03Completed) == 1
}

// SetC03Completed 设置C03完成状态（原子操作）
func (urs *UnifiedRoundStatus) SetC03Completed(completed bool) {
	var value int32
	if completed {
		value = 1
	}
	atomic.StoreInt32(&urs.c03Completed, value)
	urs.mu.Lock()
	urs.LastUpdateTime = time.Now()
	urs.mu.Unlock()
}

// IsRoundCompleted 检查当前轮次是否完成
func (urs *UnifiedRoundStatus) IsRoundCompleted() bool {
	return urs.IsB02Completed() && urs.IsC03Completed()
}

// GetStatus 获取状态（原子操作）
func (urs *UnifiedRoundStatus) GetStatus() RoundStatusType {
	return RoundStatusType(atomic.LoadInt32(&urs.status))
}

// SetStatus 设置状态（原子操作）
func (urs *UnifiedRoundStatus) SetStatus(status RoundStatusType) {
	atomic.StoreInt32(&urs.status, int32(status))
	urs.mu.Lock()
	urs.LastUpdateTime = time.Now()
	urs.mu.Unlock()
}

// IncrementErrorCount 增加错误计数（原子操作）
func (urs *UnifiedRoundStatus) IncrementErrorCount() int {
	return int(atomic.AddInt32(&urs.errorCount, 1))
}

// IncrementRetryCount 增加重试计数（原子操作）
func (urs *UnifiedRoundStatus) IncrementRetryCount() int {
	return int(atomic.AddInt32(&urs.retryCount, 1))
}

// MarkCompleted 标记轮次完成
func (urs *UnifiedRoundStatus) MarkCompleted() {
	now := time.Now()
	urs.mu.Lock()
	urs.CompletionTime = &now
	urs.LastUpdateTime = now
	urs.mu.Unlock()
	urs.SetStatus(RoundStatusCompleted)
}

// EventBus 事件总线
type EventBus struct {
	handlers map[RoundEventType][]EventHandler
	mu       sync.RWMutex
	running  bool
	eventCh  chan RoundEvent
	ctx      context.Context
	cancel   context.CancelFunc
}

// NewEventBus 创建新的事件总线
func NewEventBus() *EventBus {
	ctx, cancel := context.WithCancel(context.Background())
	return &EventBus{
		handlers: make(map[RoundEventType][]EventHandler),
		eventCh:  make(chan RoundEvent, 100), // 缓冲100个事件
		ctx:      ctx,
		cancel:   cancel,
	}
}

// Subscribe 订阅事件
func (eb *EventBus) Subscribe(eventType RoundEventType, handler EventHandler) {
	eb.mu.Lock()
	defer eb.mu.Unlock()
	
	eb.handlers[eventType] = append(eb.handlers[eventType], handler)
}

// Publish 发布事件
func (eb *EventBus) Publish(event RoundEvent) {
	if !eb.running {
		return
	}
	
	select {
	case eb.eventCh <- event:
	case <-eb.ctx.Done():
		return
	default:
		// 事件队列满，丢弃事件
		utils.LogWarning("事件队列满，丢弃事件",
			zap.String("event_type", string(event.Type)),
			zap.String("user_key", event.UserKey),
		)
	}
}

// Start 启动事件总线
func (eb *EventBus) Start() {
	eb.mu.Lock()
	if eb.running {
		eb.mu.Unlock()
		return
	}
	eb.running = true
	eb.mu.Unlock()
	
	go eb.eventLoop()
}

// Stop 停止事件总线
func (eb *EventBus) Stop() {
	eb.mu.Lock()
	if !eb.running {
		eb.mu.Unlock()
		return
	}
	eb.running = false
	eb.mu.Unlock()
	
	eb.cancel()
}

// eventLoop 事件循环
func (eb *EventBus) eventLoop() {
	for {
		select {
		case event := <-eb.eventCh:
			eb.handleEvent(event)
		case <-eb.ctx.Done():
			return
		}
	}
}

// handleEvent 处理事件
func (eb *EventBus) handleEvent(event RoundEvent) {
	eb.mu.RLock()
	handlers, exists := eb.handlers[event.Type]
	eb.mu.RUnlock()
	
	if !exists {
		return
	}
	
	for _, handler := range handlers {
		go func(h EventHandler) {
			defer func() {
				if r := recover(); r != nil {
					utils.LogError("事件处理器异常",
						zap.String("event_type", string(event.Type)),
						zap.Any("panic", r),
					)
				}
			}()
			if err := h(event); err != nil {
				utils.LogError("事件处理失败",
					zap.String("event_type", string(event.Type)),
					zap.String("user_key", event.UserKey),
					zap.Error(err),
				)
			}
		}(handler)
	}
}

// DataManager 数据管理器
type DataManager struct {
	userData map[string]*models.CurrentUserCheckingInfo
	mu       sync.RWMutex
}

// NewDataManager 创建新的数据管理器
func NewDataManager() *DataManager {
	return &DataManager{
		userData: make(map[string]*models.CurrentUserCheckingInfo),
	}
}

// GetUserData 获取用户数据
func (dm *DataManager) GetUserData(userKey string) *models.CurrentUserCheckingInfo {
	dm.mu.RLock()
	defer dm.mu.RUnlock()
	
	return dm.userData[userKey]
}

// SetUserData 设置用户数据
func (dm *DataManager) SetUserData(userKey string, data *models.CurrentUserCheckingInfo) {
	dm.mu.Lock()
	defer dm.mu.Unlock()
	
	dm.userData[userKey] = data
}

// UpdateUserData 更新用户数据
func (dm *DataManager) UpdateUserData(userKey string, updateFunc func(*models.CurrentUserCheckingInfo)) error {
	dm.mu.Lock()
	defer dm.mu.Unlock()
	
	data, exists := dm.userData[userKey]
	if !exists {
		return fmt.Errorf("用户数据不存在: %s", userKey)
	}
	
	updateFunc(data)
	return nil
}

// DeleteUserData 删除用户数据
func (dm *DataManager) DeleteUserData(userKey string) {
	dm.mu.Lock()
	defer dm.mu.Unlock()
	
	delete(dm.userData, userKey)
}

// GetAllUserData 获取所有用户数据
func (dm *DataManager) GetAllUserData() map[string]*models.CurrentUserCheckingInfo {
	dm.mu.RLock()
	defer dm.mu.RUnlock()
	
	result := make(map[string]*models.CurrentUserCheckingInfo, len(dm.userData))
	for userKey, data := range dm.userData {
		result[userKey] = data
	}
	
	return result
}

// RoundManagerInterface 轮次管理器接口
type RoundManagerInterface interface {
	// 轮次操作
	StartRound(userKey string) error
	CompleteMode(userKey string, mode string) (int, bool, int, error)
	GetCurrentRound(userKey string) int
	GetRoundStatus(userKey string) *UnifiedRoundStatus
	
	// 事件管理
	Subscribe(eventType RoundEventType, handler EventHandler)
	Unsubscribe(eventType RoundEventType, handler EventHandler)
	PublishEvent(event RoundEvent)
	
	// 数据管理
	GetUserData(userKey string) *models.CurrentUserCheckingInfo
	SyncData(userKey string) error
	
	// 生命周期
	Start() error
	Stop() error
	IsRunning() bool
}

// UnifiedRoundManager 统一轮次管理器
type UnifiedRoundManager struct {
	// 核心状态
	rounds      map[string]*UnifiedRoundStatus // 轮次映射
	globalMutex sync.RWMutex                   // 全局读写锁
	
	// 组件引用
	dataManager   *DataManager // 数据管理器
	screenshotMgr *ScreenshotRoundManager // 截图管理器
	
	// 事件系统
	eventBus  *EventBus                      // 事件总线
	callbacks map[string][]EventHandler      // 回调函数映射
	
	// 配置参数
	maxRounds    int           // 最大轮次数
	syncInterval time.Duration // 同步间隔
	
	// 运行状态
	ctx     context.Context    // 上下文
	cancel  context.CancelFunc // 取消函数
	running int32              // 运行状态（原子操作）
	
	// 性能指标
	metrics *Metrics
}

// Metrics 性能指标
type Metrics struct {
	RoundCreated     int64         `json:"round_created"`
	RoundCompleted   int64         `json:"round_completed"`
	ModeCompleted    int64         `json:"mode_completed"`
	ErrorCount       int64         `json:"error_count"`
	AvgRoundDuration time.Duration `json:"avg_round_duration"`
	ConcurrentUsers  int64         `json:"concurrent_users"`
}

// NewUnifiedRoundManager 创建新的统一轮次管理器
func NewUnifiedRoundManager(screenshotMgr *ScreenshotRoundManager) *UnifiedRoundManager {
	ctx, cancel := context.WithCancel(context.Background())
	
	return &UnifiedRoundManager{
		rounds:        make(map[string]*UnifiedRoundStatus),
		dataManager:   NewDataManager(),
		screenshotMgr: screenshotMgr,
		eventBus:      NewEventBus(),
		callbacks:     make(map[string][]RoundCallback),
		maxRounds:     10,
		syncInterval:  time.Second * 30,
		ctx:           ctx,
		cancel:        cancel,
		running:       0,
		metrics:       &Metrics{},
	}
}

// Start 启动管理器
func (urm *UnifiedRoundManager) Start() error {
	if !atomic.CompareAndSwapInt32(&urm.running, 0, 1) {
		return fmt.Errorf("管理器已在运行中")
	}
	
	utils.LogInfo("统一轮次管理器启动")
	
	// 启动同步协程
	go urm.syncRoutine()
	
	return nil
}

// Stop 停止管理器
func (urm *UnifiedRoundManager) Stop() error {
	if !atomic.CompareAndSwapInt32(&urm.running, 1, 0) {
		return fmt.Errorf("管理器未在运行")
	}
	
	urm.cancel()
	utils.LogInfo("统一轮次管理器停止")
	return nil
}

// IsRunning 检查是否在运行
func (urm *UnifiedRoundManager) IsRunning() bool {
	return atomic.LoadInt32(&urm.running) == 1
}

// syncRoutine 同步协程
func (urm *UnifiedRoundManager) syncRoutine() {
	ticker := time.NewTicker(urm.syncInterval)
	defer ticker.Stop()
	
	for {
		select {
		case <-ticker.C:
			urm.performSync()
		case <-urm.ctx.Done():
			return
		}
	}
}

// performSync 执行同步
func (urm *UnifiedRoundManager) performSync() {
	urm.globalMutex.RLock()
	userKeys := make([]string, 0, len(urm.rounds))
	for userKey := range urm.rounds {
		userKeys = append(userKeys, userKey)
	}
	urm.globalMutex.RUnlock()
	
	for _, userKey := range userKeys {
		if err := urm.SyncData(userKey); err != nil {
			utils.LogInfo("数据同步失败",
				zap.String("user_key", userKey),
				zap.Error(err),
			)
		}
	}
}

// generateUserKey 生成用户唯一标识
func (urm *UnifiedRoundManager) generateUserKey(userName string) string {
	today := time.Now().Format("20060102")
	return fmt.Sprintf("%s_%s", userName, today)
}

// GetCurrentRound 获取当前轮次
func (urm *UnifiedRoundManager) GetCurrentRound(userName string) int {
	userKey := urm.generateUserKey(userName)
	
	urm.globalMutex.RLock()
	roundStatus, exists := urm.rounds[userKey]
	urm.globalMutex.RUnlock()
	
	if !exists {
		// 创建新的轮次状态
		roundStatus = urm.createRoundStatus(userKey, userName)
	}
	
	return roundStatus.GetCurrentRound()
}

// createRoundStatus 创建轮次状态
func (urm *UnifiedRoundManager) createRoundStatus(userKey, userName string) *UnifiedRoundStatus {
	urm.globalMutex.Lock()
	defer urm.globalMutex.Unlock()
	
	// 双重检查
	if roundStatus, exists := urm.rounds[userKey]; exists {
		return roundStatus
	}
	
	roundStatus := NewUnifiedRoundStatus(userKey, userName)
	urm.rounds[userKey] = roundStatus
	
	// 更新指标
	atomic.AddInt64(&urm.metrics.RoundCreated, 1)
	atomic.AddInt64(&urm.metrics.ConcurrentUsers, 1)
	
	utils.LogInfo("创建新轮次状态",
		zap.String("user_key", userKey),
		zap.String("user_name", userName),
	)
	
	return roundStatus
}

// GetRoundStatus 获取轮次状态
func (urm *UnifiedRoundManager) GetRoundStatus(userName string) *UnifiedRoundStatus {
	userKey := urm.generateUserKey(userName)
	
	urm.globalMutex.RLock()
	roundStatus, exists := urm.rounds[userKey]
	urm.globalMutex.RUnlock()
	
	if !exists {
		roundStatus = urm.createRoundStatus(userKey, userName)
	}
	
	return roundStatus
}

// StartRound 启动轮次
func (urm *UnifiedRoundManager) StartRound(userKey string) error {
	urm.globalMutex.RLock()
	roundStatus, exists := urm.rounds[userKey]
	urm.globalMutex.RUnlock()
	
	if !exists {
		return fmt.Errorf("轮次状态不存在: %s", userKey)
	}
	
	roundStatus.SetStatus(RoundStatusInProgress)
	
	// 发布轮次开始事件
	event := RoundEvent{
		Type:      EventRoundStarted,
		UserKey:   userKey,
		Round:     roundStatus.GetCurrentRound(),
		Timestamp: time.Now(),
	}
	urm.PublishEvent(event)
	
	utils.LogInfo("轮次启动",
		zap.String("user_key", userKey),
		zap.Int("round", roundStatus.GetCurrentRound()),
	)
	
	return nil
}

// CompleteMode 完成模式
func (urm *UnifiedRoundManager) CompleteMode(userName string, mode string) (int, bool, int, error) {
	userKey := urm.generateUserKey(userName)
	roundStatus := urm.GetRoundStatus(userName)
	
	utils.LogInfo("开始标记模式完成",
		zap.String("user_key", userKey),
		zap.String("user_name", userName),
		zap.String("mode", mode),
	)
	
	// 标记前状态
	currentRound := roundStatus.GetCurrentRound()
	b02Before := roundStatus.IsB02Completed()
	c03Before := roundStatus.IsC03Completed()
	
	utils.LogInfo("标记前状态",
		zap.Int("current_round", currentRound),
		zap.Bool("b02_completed", b02Before),
		zap.Bool("c03_completed", c03Before),
	)
	
	// 根据模式标记完成状态
	var normalizedMode string
	switch mode {
	case "生化平衡分析", "B", "B02":
		roundStatus.SetB02Completed(true)
		normalizedMode = "B"
		utils.LogInfo("标记B02模式完成")
		
		// 发布模式完成事件
		event := RoundEvent{
			Type:      EventModeCompleted,
			UserKey:   userKey,
			Round:     currentRound,
			Mode:      "B02",
			Timestamp: time.Now(),
		}
		urm.PublishEvent(event)
		
	case "病理形态学分析", "C", "C03":
		roundStatus.SetC03Completed(true)
		normalizedMode = "C"
		utils.LogInfo("标记C03模式完成")
		
		// 发布模式完成事件
		event := RoundEvent{
			Type:      EventModeCompleted,
			UserKey:   userKey,
			Round:     currentRound,
			Mode:      "C03",
			Timestamp: time.Now(),
		}
		urm.PublishEvent(event)
		
	default:
		utils.LogWarning("未识别的模式", zap.String("mode", mode))
		normalizedMode = mode
	}
	
	// 更新指标
	atomic.AddInt64(&urm.metrics.ModeCompleted, 1)
	
	// 检查轮次是否完成
	roundCompleted := roundStatus.IsRoundCompleted()
	var nextRound int
	
	utils.LogInfo("标记后状态",
		zap.Int("current_round", currentRound),
		zap.Bool("b02_completed", roundStatus.IsB02Completed()),
		zap.Bool("c03_completed", roundStatus.IsC03Completed()),
		zap.Bool("round_completed", roundCompleted),
	)
	
	if roundCompleted {
		// 标记轮次完成
		roundStatus.MarkCompleted()
		
		// 发布轮次完成事件
		event := RoundEvent{
			Type:      EventRoundCompleted,
			UserKey:   userKey,
			Round:     currentRound,
			Timestamp: time.Now(),
		}
		urm.PublishEvent(event)
		
		// 更新指标
		atomic.AddInt64(&urm.metrics.RoundCompleted, 1)
		
		if currentRound < urm.maxRounds {
			// 进入下一轮
			nextRound = currentRound + 1
			utils.LogInfo("轮次完成，准备进入下一轮",
				zap.Int("completed_round", currentRound),
				zap.Int("next_round", nextRound),
			)
			
			// 重置状态，准备下一轮次
			roundStatus.SetCurrentRound(nextRound)
			roundStatus.SetB02Completed(false)
			roundStatus.SetC03Completed(false)
			roundStatus.SetStatus(RoundStatusPending)
			
			utils.LogInfo("状态已重置，当前轮次更新",
				zap.Int("new_current_round", nextRound),
			)
		} else {
			// 所有轮次已完成
			nextRound = urm.maxRounds + 1 // 标记为超过最大轮次
			utils.LogInfo("所有轮次已完成",
				zap.Int("total_rounds", urm.maxRounds),
			)
			
			// 发布所有轮次完成事件
			allCompleteEvent := RoundEvent{
				Type:      EventRoundCompleted,
				UserKey:   userKey,
				Round:     urm.maxRounds,
				Data:      map[string]interface{}{"all_completed": true},
				Timestamp: time.Now(),
			}
			urm.PublishEvent(allCompleteEvent)
		}
	} else {
		// 轮次未完成，保持当前轮次
		nextRound = currentRound
		utils.LogInfo("轮次未完成，保持当前轮次",
			zap.Int("current_round", currentRound),
		)
	}
	
	// 同步数据到数据管理器
	if err := urm.syncRoundData(userKey, roundStatus); err != nil {
		utils.LogError("同步轮次数据失败",
			zap.String("user_key", userKey),
			zap.Error(err),
		)
	}
	
	return currentRound, roundCompleted, nextRound, nil
}

func (urm *UnifiedRoundManager) Subscribe(eventType RoundEventType, handler EventHandler) {
	urm.eventBus.Subscribe(eventType, handler)
}



func (urm *UnifiedRoundManager) PublishEvent(event RoundEvent) {
	urm.eventBus.Publish(event)
}

func (urm *UnifiedRoundManager) GetUserData(userKey string) *models.CurrentUserCheckingInfo {
	return urm.dataManager.GetUserData(userKey)
}

// SyncData 同步数据
func (urm *UnifiedRoundManager) SyncData(userKey string) error {
	urm.globalMutex.RLock()
	roundStatus, exists := urm.rounds[userKey]
	urm.globalMutex.RUnlock()
	
	if !exists {
		return fmt.Errorf("轮次状态不存在: %s", userKey)
	}
	
	return urm.syncRoundData(userKey, roundStatus)
}

// syncRoundData 同步轮次数据到数据管理器
func (urm *UnifiedRoundManager) syncRoundData(userKey string, roundStatus *UnifiedRoundStatus) error {
	// 获取或创建用户检测信息
	userData := urm.dataManager.GetUserData(userKey)
	if userData == nil {
		// 创建新的用户检测信息
		userData = models.NewCurrentUserCheckingInfo(
			"", // UserID 待填充
			roundStatus.UserName,
			"", // SiteName 待填充
			"", // RegistrationNo 待填充
		)
		urm.dataManager.SetUserData(userKey, userData)
	}
	
	// 更新轮次信息
	userData.CompletedRounds = roundStatus.GetCurrentRound()
	if roundStatus.GetCurrentRound() > urm.maxRounds {
		userData.CompletedRounds = urm.maxRounds
		userData.MarkCompleted()
	}
	
	// 发布数据同步事件
	event := RoundEvent{
		Type:      EventDataSync,
		UserKey:   userKey,
		Round:     roundStatus.GetCurrentRound(),
		Data:      userData,
		Timestamp: time.Now(),
	}
	urm.PublishEvent(event)
	
	return nil
}

// GetMetrics 获取性能指标
func (urm *UnifiedRoundManager) GetMetrics() *Metrics {
	return urm.metrics
}

// CleanupExpiredRounds 清理过期轮次
func (urm *UnifiedRoundManager) CleanupExpiredRounds(expireDuration time.Duration) {
	now := time.Now()
	expiredKeys := make([]string, 0)
	
	urm.globalMutex.RLock()
	for userKey, roundStatus := range urm.rounds {
		roundStatus.mu.RLock()
		if now.Sub(roundStatus.LastUpdateTime) > expireDuration {
			expiredKeys = append(expiredKeys, userKey)
		}
		roundStatus.mu.RUnlock()
	}
	urm.globalMutex.RUnlock()
	
	if len(expiredKeys) > 0 {
		urm.globalMutex.Lock()
		for _, userKey := range expiredKeys {
			delete(urm.rounds, userKey)
			atomic.AddInt64(&urm.metrics.ConcurrentUsers, -1)
		}
		urm.globalMutex.Unlock()
		
		utils.LogInfo("清理过期轮次",
			zap.Int("expired_count", len(expiredKeys)),
			zap.Duration("expire_duration", expireDuration),
		)
	}
}

// GetAllRoundStatus 获取所有轮次状态（用于调试和监控）
func (urm *UnifiedRoundManager) GetAllRoundStatus() map[string]*UnifiedRoundStatus {
	urm.globalMutex.RLock()
	defer urm.globalMutex.RUnlock()
	
	result := make(map[string]*UnifiedRoundStatus, len(urm.rounds))
	for userKey, roundStatus := range urm.rounds {
		result[userKey] = roundStatus
	}
	
	return result
}

// MarkModeCompleted 兼容性方法 - 对接原有的 markModeCompleted 函数
func (urm *UnifiedRoundManager) MarkModeCompleted(userName, mode string) (int, bool, int) {
	currentRound, roundCompleted, nextRound, err := urm.CompleteMode(userName, mode)
	if err != nil {
		utils.LogError("标记模式完成失败",
			zap.String("user_name", userName),
			zap.String("mode", mode),
			zap.Error(err),
		)
		// 返回默认值
		return 1, false, 1
	}
	return currentRound, roundCompleted, nextRound
}

// GetCurrentRoundByUserKey 通过用户键获取当前轮次
func (urm *UnifiedRoundManager) GetCurrentRoundByUserKey(userKey string) int {
	urm.globalMutex.RLock()
	roundStatus, exists := urm.rounds[userKey]
	urm.globalMutex.RUnlock()
	
	if !exists {
		return 1 // 默认返回第1轮
	}
	
	return roundStatus.GetCurrentRound()
}