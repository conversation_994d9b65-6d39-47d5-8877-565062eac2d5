{"timestamp": "2025-07-01 17:40:31", "image_path": "pic\\temp\\temp_screenshot_B_暂无候检者_20250701_174001.png", "ocr_data_path": "<直接传入JSON数据>", "total_rows": 64, "successful_rows": 64, "processing_time": "273.959ms", "color_distribution": {"橘色": 14, "红色": 8, "蓝色": 42}, "analysis_results": [{"row_index": 0, "numeric_value": "按照标准图谱相似度递减列表：", "text_elements": ["0.000"], "regions": {"digit_left": {"region_type": "digit_left", "main_color": "蓝色", "rgb": [90, 152, 210], "confidence": 0.6456211812627292, "bounds": [18, 27, 269, 63]}, "digit_right": {"region_type": "digit_right", "main_color": "蓝色", "rgb": [91, 154, 209], "confidence": 0.6495071193866374, "bounds": [269, 27, 521, 63]}, "text_left": {"region_type": "text_left", "main_color": "蓝色", "rgb": [78, 113, 201], "confidence": 1, "bounds": [100, 77, 128, 102]}, "text_right": {"region_type": "text_right", "main_color": "蓝色", "rgb": [82, 112, 200], "confidence": 1, "bounds": [128, 77, 157, 102]}}, "final_color": "蓝色", "confidence": 1}, {"row_index": 1, "numeric_value": "经厚在第2暖推水平横截面", "text_elements": ["3.896"], "regions": {"digit_left": {"region_type": "digit_left", "main_color": "蓝色", "rgb": [93, 97, 175], "confidence": 1, "bounds": [190, 75, 306, 102]}, "digit_right": {"region_type": "digit_right", "main_color": "蓝色", "rgb": [93, 93, 170], "confidence": 1, "bounds": [306, 75, 423, 102]}, "text_left": {"region_type": "text_left", "main_color": "蓝色", "rgb": [78, 106, 194], "confidence": 1, "bounds": [98, 102, 127, 129]}, "text_right": {"region_type": "text_right", "main_color": "蓝色", "rgb": [78, 110, 199], "confidence": 1, "bounds": [127, 102, 157, 129]}}, "final_color": "蓝色", "confidence": 1}, {"row_index": 2, "numeric_value": "优化配置", "text_elements": ["虚拟模式-健康问题发展趋势列表："], "regions": {"digit_left": {"region_type": "digit_left", "main_color": "蓝色", "rgb": [95, 95, 174], "confidence": 1, "bounds": [192, 102, 231, 129]}, "digit_right": {"region_type": "digit_right", "main_color": "蓝色", "rgb": [100, 100, 173], "confidence": 1, "bounds": [231, 102, 270, 129]}, "text_left": {"region_type": "text_left", "main_color": "蓝色", "rgb": [100, 100, 174], "confidence": 1, "bounds": [194, 127, 328, 152]}, "text_right": {"region_type": "text_right", "main_color": "蓝色", "rgb": [101, 101, 174], "confidence": 1, "bounds": [328, 127, 462, 152]}}, "final_color": "蓝色", "confidence": 1}, {"row_index": 3, "numeric_value": "0.049", "text_elements": ["C反应蛋白C-REACTIVEPROTEIN"], "regions": {"digit_left": {"region_type": "digit_left", "main_color": "红色", "rgb": [255, 24, 40], "confidence": 0.7722222222222223, "bounds": [98, 154, 129, 179]}, "digit_right": {"region_type": "digit_right", "main_color": "红色", "rgb": [255, 28, 39], "confidence": 0.8237885462555066, "bounds": [129, 154, 161, 179]}, "text_left": {"region_type": "text_left", "main_color": "蓝色", "rgb": [91, 125, 170], "confidence": 1, "bounds": [192, 152, 343, 179]}, "text_right": {"region_type": "text_right", "main_color": "蓝色", "rgb": [87, 132, 184], "confidence": 1, "bounds": [343, 152, 495, 179]}}, "final_color": "红色", "confidence": 0.8237885462555066}, {"row_index": 4, "numeric_value": "0.058", "text_elements": ["血尿酸SERUMURICACID"], "regions": {"digit_left": {"region_type": "digit_left", "main_color": "红色", "rgb": [255, 22, 37], "confidence": 0.7513513513513513, "bounds": [98, 179, 130, 204]}, "digit_right": {"region_type": "digit_right", "main_color": "红色", "rgb": [255, 30, 43], "confidence": 0.7868852459016393, "bounds": [130, 179, 162, 204]}, "text_left": {"region_type": "text_left", "main_color": "红色", "rgb": [254, 43, 46], "confidence": 0.9260533104041273, "bounds": [194, 181, 308, 204]}, "text_right": {"region_type": "text_right", "main_color": "红色", "rgb": [255, 28, 39], "confidence": 0.8195488721804511, "bounds": [308, 181, 423, 204]}}, "final_color": "红色", "confidence": 0.9260533104041273}, {"row_index": 5, "numeric_value": "0.064", "text_elements": ["脂肪酶*"], "regions": {"digit_left": {"region_type": "digit_left", "main_color": "红色", "rgb": [255, 25, 40], "confidence": 0.7675675675675676, "bounds": [98, 204, 130, 229]}, "digit_right": {"region_type": "digit_right", "main_color": "红色", "rgb": [255, 33, 49], "confidence": 0.8225108225108225, "bounds": [130, 204, 162, 229]}, "text_left": {"region_type": "text_left", "main_color": "红色", "rgb": [254, 53, 53], "confidence": 1, "bounds": [192, 204, 229, 231]}, "text_right": {"region_type": "text_right", "main_color": "红色", "rgb": [255, 53, 54], "confidence": 0.9798488664987406, "bounds": [229, 204, 266, 231]}}, "final_color": "红色", "confidence": 1}, {"row_index": 6, "numeric_value": "0.067", "text_elements": ["血管紧张素Ⅱ*"], "regions": {"digit_left": {"region_type": "digit_left", "main_color": "红色", "rgb": [255, 22, 37], "confidence": 0.7555555555555555, "bounds": [98, 229, 129, 256]}, "digit_right": {"region_type": "digit_right", "main_color": "红色", "rgb": [255, 27, 40], "confidence": 0.8009259259259259, "bounds": [129, 229, 161, 256]}, "text_left": {"region_type": "text_left", "main_color": "红色", "rgb": [254, 60, 60], "confidence": 1, "bounds": [192, 227, 256, 258]}, "text_right": {"region_type": "text_right", "main_color": "红色", "rgb": [255, 58, 61], "confidence": 0.9477848101265823, "bounds": [256, 227, 320, 258]}}, "final_color": "红色", "confidence": 1}, {"row_index": 7, "numeric_value": "0.078", "text_elements": ["肥固醇COMMONPLASMACHOLESTERIN"], "regions": {"digit_left": {"region_type": "digit_left", "main_color": "红色", "rgb": [255, 25, 40], "confidence": 0.7675675675675676, "bounds": [98, 256, 130, 281]}, "digit_right": {"region_type": "digit_right", "main_color": "红色", "rgb": [255, 27, 40], "confidence": 0.8068669527896996, "bounds": [130, 256, 162, 281]}, "text_left": {"region_type": "text_left", "main_color": "红色", "rgb": [255, 43, 49], "confidence": 0.881975625400898, "bounds": [194, 259, 374, 277]}, "text_right": {"region_type": "text_right", "main_color": "红色", "rgb": [255, 30, 43], "confidence": 0.7921985815602837, "bounds": [374, 259, 554, 277]}}, "final_color": "红色", "confidence": 0.881975625400898}, {"row_index": 8, "numeric_value": "0.088", "text_elements": ["血浆丰化脂肪酸NONETHERIZED FATTYACIDSOF PLASMA"], "regions": {"digit_left": {"region_type": "digit_left", "main_color": "红色", "rgb": [255, 22, 37], "confidence": 0.7513513513513513, "bounds": [98, 281, 130, 306]}, "digit_right": {"region_type": "digit_right", "main_color": "红色", "rgb": [255, 30, 43], "confidence": 0.7961538461538461, "bounds": [130, 281, 162, 306]}, "text_left": {"region_type": "text_left", "main_color": "红色", "rgb": [254, 46, 49], "confidence": 0.9376576576576576, "bounds": [194, 282, 460, 306]}, "text_right": {"region_type": "text_right", "main_color": "红色", "rgb": [255, 29, 41], "confidence": 0.8176470588235294, "bounds": [460, 282, 727, 306]}}, "final_color": "红色", "confidence": 0.9376576576576576}, {"row_index": 9, "numeric_value": "0.089", "text_elements": ["血管紧张素I*"], "regions": {"digit_left": {"region_type": "digit_left", "main_color": "红色", "rgb": [255, 24, 40], "confidence": 0.7722222222222223, "bounds": [98, 306, 129, 332]}, "digit_right": {"region_type": "digit_right", "main_color": "红色", "rgb": [255, 28, 42], "confidence": 0.7952755905511811, "bounds": [129, 306, 161, 332]}, "text_left": {"region_type": "text_left", "main_color": "红色", "rgb": [255, 57, 57], "confidence": 1, "bounds": [194, 307, 252, 327]}, "text_right": {"region_type": "text_right", "main_color": "红色", "rgb": [255, 63, 64], "confidence": 0.9748427672955975, "bounds": [252, 307, 310, 327]}}, "final_color": "红色", "confidence": 1}, {"row_index": 10, "numeric_value": "0.137", "text_elements": ["血钾PLASMAPOTASSIUM"], "regions": {"digit_left": {"region_type": "digit_left", "main_color": "红色", "rgb": [255, 22, 33], "confidence": 0.7375, "bounds": [98, 332, 129, 358]}, "digit_right": {"region_type": "digit_right", "main_color": "红色", "rgb": [255, 36, 46], "confidence": 0.8421052631578947, "bounds": [129, 332, 161, 358]}, "text_left": {"region_type": "text_left", "main_color": "红色", "rgb": [254, 38, 42], "confidence": 0.9063475546305931, "bounds": [196, 336, 311, 354]}, "text_right": {"region_type": "text_right", "main_color": "红色", "rgb": [255, 30, 40], "confidence": 0.813441483198146, "bounds": [311, 336, 426, 354]}}, "final_color": "红色", "confidence": 0.9063475546305931}, {"row_index": 11, "numeric_value": "0.065", "text_elements": ["血清蛋白SERUM ALBUMEN"], "regions": {"digit_left": {"region_type": "digit_left", "main_color": "橘色", "rgb": [239, 137, 61], "confidence": 0.8925619834710744, "bounds": [98, 358, 127, 383]}, "digit_right": {"region_type": "digit_right", "main_color": "橘色", "rgb": [237, 137, 59], "confidence": 0.9181286549707602, "bounds": [127, 358, 157, 383]}, "text_left": {"region_type": "text_left", "main_color": "橘色", "rgb": [236, 133, 65], "confidence": 0.9485924112607099, "bounds": [194, 359, 303, 383]}, "text_right": {"region_type": "text_right", "main_color": "橘色", "rgb": [238, 137, 60], "confidence": 0.8719611021069692, "bounds": [303, 359, 412, 383]}}, "final_color": "橘色", "confidence": 0.9485924112607099}, {"row_index": 12, "numeric_value": "0.079", "text_elements": ["PERIPHERICBLOODLEUCOCYTES"], "regions": {"digit_left": {"region_type": "digit_left", "main_color": "橘色", "rgb": [239, 138, 60], "confidence": 0.905982905982906, "bounds": [98, 383, 126, 409]}, "digit_right": {"region_type": "digit_right", "main_color": "橘色", "rgb": [238, 137, 59], "confidence": 0.925, "bounds": [126, 383, 155, 409]}, "text_left": {"region_type": "text_left", "main_color": "橘色", "rgb": [238, 136, 61], "confidence": 0.8893956670467503, "bounds": [194, 386, 333, 404]}, "text_right": {"region_type": "text_right", "main_color": "橘色", "rgb": [237, 133, 55], "confidence": 0.9206145966709347, "bounds": [333, 386, 473, 404]}}, "final_color": "橘色", "confidence": 0.925}, {"row_index": 13, "numeric_value": "0.082", "text_elements": ["尿中蛋白质PROTEININURINE"], "regions": {"digit_left": {"region_type": "digit_left", "main_color": "橘色", "rgb": [239, 138, 61], "confidence": 0.8888888888888888, "bounds": [98, 408, 126, 434]}, "digit_right": {"region_type": "digit_right", "main_color": "橘色", "rgb": [238, 138, 59], "confidence": 0.9142857142857143, "bounds": [126, 408, 155, 434]}, "text_left": {"region_type": "text_left", "main_color": "橘色", "rgb": [236, 133, 65], "confidence": 0.9412429378531073, "bounds": [194, 409, 318, 433]}, "text_right": {"region_type": "text_right", "main_color": "橘色", "rgb": [238, 137, 61], "confidence": 0.852054794520548, "bounds": [318, 409, 443, 433]}}, "final_color": "橘色", "confidence": 0.9412429378531073}, {"row_index": 14, "numeric_value": "0.083", "text_elements": ["血红蛋白HAEMOGLOBIN"], "regions": {"digit_left": {"region_type": "digit_left", "main_color": "橘色", "rgb": [239, 138, 60], "confidence": 0.905982905982906, "bounds": [98, 433, 126, 459]}, "digit_right": {"region_type": "digit_right", "main_color": "橘色", "rgb": [238, 138, 59], "confidence": 0.9213483146067416, "bounds": [126, 433, 155, 459]}, "text_left": {"region_type": "text_left", "main_color": "橘色", "rgb": [236, 133, 65], "confidence": 0.946376811594203, "bounds": [194, 436, 293, 459]}, "text_right": {"region_type": "text_right", "main_color": "橘色", "rgb": [238, 136, 58], "confidence": 0.8871473354231975, "bounds": [293, 436, 393, 459]}}, "final_color": "橘色", "confidence": 0.946376811594203}, {"row_index": 15, "numeric_value": "0.087", "text_elements": ["分段的中性粒细胞SEGMENTEDNEUTROPHILS"], "regions": {"digit_left": {"region_type": "digit_left", "main_color": "橘色", "rgb": [239, 138, 61], "confidence": 0.8888888888888888, "bounds": [98, 458, 126, 484]}, "digit_right": {"region_type": "digit_right", "main_color": "橘色", "rgb": [238, 140, 59], "confidence": 0.9202453987730062, "bounds": [126, 458, 155, 484]}, "text_left": {"region_type": "text_left", "main_color": "橘色", "rgb": [235, 132, 65], "confidence": 0.9339525283797729, "bounds": [194, 461, 379, 479]}, "text_right": {"region_type": "text_right", "main_color": "橘色", "rgb": [238, 135, 57], "confidence": 0.8817317845828934, "bounds": [379, 461, 565, 479]}}, "final_color": "橘色", "confidence": 0.9339525283797729}, {"row_index": 16, "numeric_value": "0.089", "text_elements": ["嗜碱性粒细胞BASOPHILS"], "regions": {"digit_left": {"region_type": "digit_left", "main_color": "橘色", "rgb": [239, 138, 60], "confidence": 0.905982905982906, "bounds": [98, 484, 126, 509]}, "digit_right": {"region_type": "digit_right", "main_color": "橘色", "rgb": [238, 140, 61], "confidence": 0.9453551912568307, "bounds": [126, 484, 155, 509]}, "text_left": {"region_type": "text_left", "main_color": "橘色", "rgb": [235, 133, 67], "confidence": 0.9418457648546145, "bounds": [192, 486, 295, 509]}, "text_right": {"region_type": "text_right", "main_color": "橘色", "rgb": [237, 136, 62], "confidence": 0.9062957540263543, "bounds": [295, 486, 399, 509]}}, "final_color": "橘色", "confidence": 0.9453551912568307}, {"row_index": 17, "numeric_value": "0.092", "text_elements": ["血红血球ERYTHROCYTES"], "regions": {"digit_left": {"region_type": "digit_left", "main_color": "橘色", "rgb": [239, 138, 61], "confidence": 0.8888888888888888, "bounds": [98, 509, 126, 536]}, "digit_right": {"region_type": "digit_right", "main_color": "橘色", "rgb": [238, 137, 56], "confidence": 0.8654970760233918, "bounds": [126, 509, 155, 536]}, "text_left": {"region_type": "text_left", "main_color": "橘色", "rgb": [236, 132, 64], "confidence": 0.9342301943198804, "bounds": [194, 511, 299, 534]}, "text_right": {"region_type": "text_right", "main_color": "橘色", "rgb": [237, 134, 57], "confidence": 0.892436974789916, "bounds": [299, 511, 404, 534]}}, "final_color": "橘色", "confidence": 0.9342301943198804}, {"row_index": 18, "numeric_value": "0.099", "text_elements": ["尿白血球URINELEUCOCYTES"], "regions": {"digit_left": {"region_type": "digit_left", "main_color": "橘色", "rgb": [239, 138, 60], "confidence": 0.905982905982906, "bounds": [98, 536, 126, 561]}, "digit_right": {"region_type": "digit_right", "main_color": "橘色", "rgb": [238, 139, 61], "confidence": 0.9217877094972067, "bounds": [126, 536, 155, 561]}, "text_left": {"region_type": "text_left", "main_color": "橘色", "rgb": [237, 133, 65], "confidence": 0.9152148664343787, "bounds": [194, 538, 316, 561]}, "text_right": {"region_type": "text_right", "main_color": "橘色", "rgb": [237, 133, 55], "confidence": 0.9199372056514914, "bounds": [316, 538, 438, 561]}}, "final_color": "橘色", "confidence": 0.9217877094972067}, {"row_index": 19, "numeric_value": "0.107", "text_elements": ["BETA球蛋白"], "regions": {"digit_left": {"region_type": "digit_left", "main_color": "橘色", "rgb": [240, 143, 65], "confidence": 0.941747572815534, "bounds": [98, 561, 126, 588]}, "digit_right": {"region_type": "digit_right", "main_color": "橘色", "rgb": [238, 133, 60], "confidence": 0.863013698630137, "bounds": [126, 561, 155, 588]}, "text_left": {"region_type": "text_left", "main_color": "橘色", "rgb": [237, 133, 57], "confidence": 0.9246575342465754, "bounds": [190, 559, 244, 588]}, "text_right": {"region_type": "text_right", "main_color": "橘色", "rgb": [236, 133, 68], "confidence": 0.9166666666666666, "bounds": [244, 559, 298, 588]}}, "final_color": "橘色", "confidence": 0.941747572815534}, {"row_index": 20, "numeric_value": "0.110", "text_elements": ["单核细胞MONOCYTES"], "regions": {"digit_left": {"region_type": "digit_left", "main_color": "橘色", "rgb": [239, 141, 62], "confidence": 0.96, "bounds": [98, 586, 126, 611]}, "digit_right": {"region_type": "digit_right", "main_color": "橘色", "rgb": [238, 131, 59], "confidence": 0.7945205479452054, "bounds": [126, 586, 155, 611]}, "text_left": {"region_type": "text_left", "main_color": "橘色", "rgb": [236, 136, 68], "confidence": 0.940387481371088, "bounds": [192, 588, 284, 611]}, "text_right": {"region_type": "text_right", "main_color": "橘色", "rgb": [237, 135, 57], "confidence": 0.9222011385199241, "bounds": [284, 588, 377, 611]}}, "final_color": "橘色", "confidence": 0.96}, {"row_index": 21, "numeric_value": "0.111", "text_elements": ["免疫球蛋白G*"], "regions": {"digit_left": {"region_type": "digit_left", "main_color": "橘色", "rgb": [239, 141, 63], "confidence": 0.9438202247191011, "bounds": [98, 611, 125, 638]}, "digit_right": {"region_type": "digit_right", "main_color": "橘色", "rgb": [239, 136, 67], "confidence": 0.7226277372262774, "bounds": [125, 611, 153, 638]}, "text_left": {"region_type": "text_left", "main_color": "橘色", "rgb": [236, 134, 70], "confidence": 0.9338235294117647, "bounds": [190, 609, 250, 640]}, "text_right": {"region_type": "text_right", "main_color": "橘色", "rgb": [236, 132, 64], "confidence": 0.9487179487179487, "bounds": [250, 609, 310, 640]}}, "final_color": "橘色", "confidence": 0.9487179487179487}, {"row_index": 22, "numeric_value": "0.114", "text_elements": ["免疫球蛋白M*"], "regions": {"digit_left": {"region_type": "digit_left", "main_color": "橘色", "rgb": [239, 141, 62], "confidence": 0.96, "bounds": [98, 636, 126, 663]}, "digit_right": {"region_type": "digit_right", "main_color": "橘色", "rgb": [237, 128, 62], "confidence": 0.7619047619047619, "bounds": [126, 636, 155, 663]}, "text_left": {"region_type": "text_left", "main_color": "橘色", "rgb": [236, 134, 69], "confidence": 0.9399538106235565, "bounds": [190, 634, 250, 663]}, "text_right": {"region_type": "text_right", "main_color": "橘色", "rgb": [237, 135, 64], "confidence": 0.91701244813278, "bounds": [250, 634, 310, 663]}}, "final_color": "橘色", "confidence": 0.96}, {"row_index": 23, "numeric_value": "0.117", "text_elements": ["血清蛋白SERUMPROTEIN"], "regions": {"digit_left": {"region_type": "digit_left", "main_color": "橘色", "rgb": [240, 143, 65], "confidence": 0.941747572815534, "bounds": [98, 663, 126, 688]}, "digit_right": {"region_type": "digit_right", "main_color": "橘色", "rgb": [237, 129, 58], "confidence": 0.7833333333333333, "bounds": [126, 663, 155, 688]}, "text_left": {"region_type": "text_left", "main_color": "橘色", "rgb": [236, 133, 65], "confidence": 0.9480354879594424, "bounds": [192, 665, 299, 690]}, "text_right": {"region_type": "text_right", "main_color": "橘色", "rgb": [238, 135, 58], "confidence": 0.8743961352657005, "bounds": [299, 665, 406, 690]}}, "final_color": "橘色", "confidence": 0.9480354879594424}, {"row_index": 24, "numeric_value": "0.174", "text_elements": ["锂*"], "regions": {"digit_left": {"region_type": "digit_left", "main_color": "橘色", "rgb": [239, 141, 62], "confidence": 0.96, "bounds": [98, 688, 126, 715]}, "digit_right": {"region_type": "digit_right", "main_color": "橘色", "rgb": [238, 132, 60], "confidence": 0.7925925925925926, "bounds": [126, 688, 155, 715]}, "text_left": {"region_type": "text_left", "main_color": "橘色", "rgb": [237, 133, 63], "confidence": 0.946236559139785, "bounds": [192, 688, 207, 713]}, "text_right": {"region_type": "text_right", "main_color": "橘色", "rgb": [235, 130, 56], "confidence": 0.9905660377358491, "bounds": [207, 688, 223, 713]}}, "final_color": "橘色", "confidence": 0.9905660377358491}, {"row_index": 25, "numeric_value": "0.063", "text_elements": ["17-血浆氧皮质类固醇类"], "regions": {"digit_left": {"region_type": "digit_left", "main_color": "蓝色", "rgb": [93, 95, 255], "confidence": 1, "bounds": [98, 713, 127, 740]}, "digit_right": {"region_type": "digit_right", "main_color": "蓝色", "rgb": [95, 100, 255], "confidence": 1, "bounds": [127, 713, 157, 740]}, "text_left": {"region_type": "text_left", "main_color": "蓝色", "rgb": [93, 93, 255], "confidence": 1, "bounds": [192, 715, 289, 740]}, "text_right": {"region_type": "text_right", "main_color": "蓝色", "rgb": [94, 94, 255], "confidence": 1, "bounds": [289, 715, 386, 740]}}, "final_color": "蓝色", "confidence": 1}, {"row_index": 26, "numeric_value": "0.066", "text_elements": ["17-尿中酮类固醇"], "regions": {"digit_left": {"region_type": "digit_left", "main_color": "蓝色", "rgb": [95, 97, 255], "confidence": 1, "bounds": [98, 740, 127, 767]}, "digit_right": {"region_type": "digit_right", "main_color": "蓝色", "rgb": [91, 95, 255], "confidence": 1, "bounds": [127, 740, 157, 767]}, "text_left": {"region_type": "text_left", "main_color": "蓝色", "rgb": [90, 90, 254], "confidence": 1, "bounds": [192, 740, 261, 765]}, "text_right": {"region_type": "text_right", "main_color": "蓝色", "rgb": [92, 92, 254], "confidence": 1, "bounds": [261, 740, 330, 765]}}, "final_color": "蓝色", "confidence": 1}, {"row_index": 27, "numeric_value": "0.067", "text_elements": ["肿瘤标志物MELANOGENE在尿"], "regions": {"digit_left": {"region_type": "digit_left", "main_color": "蓝色", "rgb": [93, 95, 255], "confidence": 1, "bounds": [98, 765, 126, 790]}, "digit_right": {"region_type": "digit_right", "main_color": "蓝色", "rgb": [94, 99, 255], "confidence": 1, "bounds": [126, 765, 155, 790]}, "text_left": {"region_type": "text_left", "main_color": "蓝色", "rgb": [92, 94, 254], "confidence": 1, "bounds": [192, 767, 318, 790]}, "text_right": {"region_type": "text_right", "main_color": "蓝色", "rgb": [90, 92, 255], "confidence": 1, "bounds": [318, 767, 445, 790]}}, "final_color": "蓝色", "confidence": 1}, {"row_index": 28, "numeric_value": "0.072", "text_elements": ["醛固酮尿*"], "regions": {"digit_left": {"region_type": "digit_left", "main_color": "蓝色", "rgb": [95, 97, 255], "confidence": 1, "bounds": [98, 790, 127, 817]}, "digit_right": {"region_type": "digit_right", "main_color": "蓝色", "rgb": [93, 97, 255], "confidence": 1, "bounds": [127, 790, 157, 817]}, "text_left": {"region_type": "text_left", "main_color": "蓝色", "rgb": [86, 86, 253], "confidence": 1, "bounds": [190, 790, 234, 817]}, "text_right": {"region_type": "text_right", "main_color": "蓝色", "rgb": [94, 93, 254], "confidence": 1, "bounds": [234, 790, 279, 817]}}, "final_color": "蓝色", "confidence": 1}, {"row_index": 29, "numeric_value": "0.074", "text_elements": ["血清溶菌酵SERUMLYSOZYME"], "regions": {"digit_left": {"region_type": "digit_left", "main_color": "蓝色", "rgb": [93, 95, 255], "confidence": 1, "bounds": [98, 815, 127, 842]}, "digit_right": {"region_type": "digit_right", "main_color": "蓝色", "rgb": [94, 97, 255], "confidence": 1, "bounds": [127, 815, 157, 842]}, "text_left": {"region_type": "text_left", "main_color": "蓝色", "rgb": [88, 89, 254], "confidence": 1, "bounds": [192, 817, 316, 840]}, "text_right": {"region_type": "text_right", "main_color": "蓝色", "rgb": [89, 95, 255], "confidence": 1, "bounds": [316, 817, 441, 840]}}, "final_color": "蓝色", "confidence": 1}, {"row_index": 30, "numeric_value": "0.075", "text_elements": ["血清补体SERUMCOMPLEMENT"], "regions": {"digit_left": {"region_type": "digit_left", "main_color": "蓝色", "rgb": [95, 97, 255], "confidence": 1, "bounds": [98, 840, 127, 867]}, "digit_right": {"region_type": "digit_right", "main_color": "蓝色", "rgb": [88, 93, 255], "confidence": 1, "bounds": [127, 840, 157, 867]}, "text_left": {"region_type": "text_left", "main_color": "蓝色", "rgb": [89, 90, 254], "confidence": 1, "bounds": [194, 844, 320, 867]}, "text_right": {"region_type": "text_right", "main_color": "蓝色", "rgb": [90, 95, 255], "confidence": 1, "bounds": [320, 844, 447, 867]}}, "final_color": "蓝色", "confidence": 1}, {"row_index": 31, "numeric_value": "0.076", "text_elements": ["ALT丙氨酸转氨酵素ALANINAMINOTRANSFERASEOFSERUM"], "regions": {"digit_left": {"region_type": "digit_left", "main_color": "蓝色", "rgb": [93, 95, 255], "confidence": 1, "bounds": [98, 867, 127, 892]}, "digit_right": {"region_type": "digit_right", "main_color": "蓝色", "rgb": [91, 95, 255], "confidence": 1, "bounds": [127, 867, 157, 892]}, "text_left": {"region_type": "text_left", "main_color": "蓝色", "rgb": [94, 95, 254], "confidence": 1, "bounds": [192, 869, 439, 892]}, "text_right": {"region_type": "text_right", "main_color": "蓝色", "rgb": [91, 95, 255], "confidence": 1, "bounds": [439, 869, 687, 892]}}, "final_color": "蓝色", "confidence": 1}, {"row_index": 32, "numeric_value": "0.076", "text_elements": ["肌酸磷酸酵素COMMONCREATINPHOSPHOKINASE"], "regions": {"digit_left": {"region_type": "digit_left", "main_color": "蓝色", "rgb": [95, 97, 255], "confidence": 1, "bounds": [98, 892, 127, 919]}, "digit_right": {"region_type": "digit_right", "main_color": "蓝色", "rgb": [91, 95, 255], "confidence": 1, "bounds": [127, 892, 157, 919]}, "text_left": {"region_type": "text_left", "main_color": "蓝色", "rgb": [91, 93, 254], "confidence": 1, "bounds": [192, 894, 399, 917]}, "text_right": {"region_type": "text_right", "main_color": "蓝色", "rgb": [87, 93, 255], "confidence": 1, "bounds": [399, 894, 606, 917]}}, "final_color": "蓝色", "confidence": 1}, {"row_index": 33, "numeric_value": "0.079", "text_elements": ["血糖BLOOD SUGAR"], "regions": {"digit_left": {"region_type": "digit_left", "main_color": "蓝色", "rgb": [93, 95, 255], "confidence": 1, "bounds": [98, 917, 127, 944]}, "digit_right": {"region_type": "digit_right", "main_color": "蓝色", "rgb": [90, 94, 255], "confidence": 1, "bounds": [127, 917, 157, 944]}, "text_left": {"region_type": "text_left", "main_color": "蓝色", "rgb": [83, 85, 254], "confidence": 1, "bounds": [194, 919, 275, 944]}, "text_right": {"region_type": "text_right", "main_color": "蓝色", "rgb": [89, 95, 255], "confidence": 1, "bounds": [275, 919, 356, 944]}}, "final_color": "蓝色", "confidence": 1}, {"row_index": 34, "numeric_value": "0.080", "text_elements": ["尿中肾上腺素URINEADRENALIN"], "regions": {"digit_left": {"region_type": "digit_left", "main_color": "蓝色", "rgb": [95, 97, 255], "confidence": 1, "bounds": [98, 944, 127, 969]}, "digit_right": {"region_type": "digit_right", "main_color": "蓝色", "rgb": [99, 103, 255], "confidence": 1, "bounds": [127, 944, 157, 969]}, "text_left": {"region_type": "text_left", "main_color": "蓝色", "rgb": [89, 90, 254], "confidence": 1, "bounds": [194, 946, 326, 969]}, "text_right": {"region_type": "text_right", "main_color": "蓝色", "rgb": [91, 96, 255], "confidence": 1, "bounds": [326, 946, 458, 969]}}, "final_color": "蓝色", "confidence": 1}, {"row_index": 35, "numeric_value": "0.082", "text_elements": ["血浆磷脂PLASMAPHOSPHOTIDES"], "regions": {"digit_left": {"region_type": "digit_left", "main_color": "蓝色", "rgb": [93, 95, 255], "confidence": 1, "bounds": [98, 969, 127, 996]}, "digit_right": {"region_type": "digit_right", "main_color": "蓝色", "rgb": [93, 97, 255], "confidence": 1, "bounds": [127, 969, 157, 996]}, "text_left": {"region_type": "text_left", "main_color": "蓝色", "rgb": [92, 93, 254], "confidence": 1, "bounds": [194, 971, 335, 994]}, "text_right": {"region_type": "text_right", "main_color": "蓝色", "rgb": [89, 94, 255], "confidence": 1, "bounds": [335, 971, 476, 994]}}, "final_color": "蓝色", "confidence": 1}, {"row_index": 36, "numeric_value": "0.083", "text_elements": ["RHEUMOFACTOR*"], "regions": {"digit_left": {"region_type": "digit_left", "main_color": "蓝色", "rgb": [95, 97, 255], "confidence": 1, "bounds": [98, 994, 127, 1021]}, "digit_right": {"region_type": "digit_right", "main_color": "蓝色", "rgb": [95, 100, 255], "confidence": 1, "bounds": [127, 994, 157, 1021]}, "text_left": {"region_type": "text_left", "main_color": "蓝色", "rgb": [90, 95, 254], "confidence": 1, "bounds": [192, 996, 269, 1019]}, "text_right": {"region_type": "text_right", "main_color": "蓝色", "rgb": [92, 94, 255], "confidence": 1, "bounds": [269, 996, 347, 1019]}}, "final_color": "蓝色", "confidence": 1}, {"row_index": 37, "numeric_value": "0.084", "text_elements": ["肾素*"], "regions": {"digit_left": {"region_type": "digit_left", "main_color": "蓝色", "rgb": [93, 95, 255], "confidence": 1, "bounds": [98, 1019, 127, 1046]}, "digit_right": {"region_type": "digit_right", "main_color": "蓝色", "rgb": [92, 95, 255], "confidence": 1, "bounds": [127, 1019, 157, 1046]}, "text_left": {"region_type": "text_left", "main_color": "蓝色", "rgb": [96, 96, 253], "confidence": 1, "bounds": [190, 1019, 217, 1047]}, "text_right": {"region_type": "text_right", "main_color": "蓝色", "rgb": [91, 91, 255], "confidence": 1, "bounds": [217, 1019, 244, 1047]}}, "final_color": "蓝色", "confidence": 1}, {"row_index": 38, "numeric_value": "0.085", "text_elements": ["血清淀粉酵素SERUMALPHAAMYLASE"], "regions": {"digit_left": {"region_type": "digit_left", "main_color": "蓝色", "rgb": [95, 97, 255], "confidence": 1, "bounds": [98, 1046, 127, 1071]}, "digit_right": {"region_type": "digit_right", "main_color": "蓝色", "rgb": [91, 95, 255], "confidence": 1, "bounds": [127, 1046, 157, 1071]}, "text_left": {"region_type": "text_left", "main_color": "蓝色", "rgb": [91, 92, 254], "confidence": 1, "bounds": [192, 1047, 348, 1071]}, "text_right": {"region_type": "text_right", "main_color": "蓝色", "rgb": [88, 93, 255], "confidence": 1, "bounds": [348, 1047, 504, 1071]}}, "final_color": "蓝色", "confidence": 1}, {"row_index": 39, "numeric_value": "0.085", "text_elements": ["游离胆固醇FREEPLASMACHOLESTERIN"], "regions": {"digit_left": {"region_type": "digit_left", "main_color": "蓝色", "rgb": [93, 95, 255], "confidence": 1, "bounds": [98, 1071, 127, 1098]}, "digit_right": {"region_type": "digit_right", "main_color": "蓝色", "rgb": [92, 96, 255], "confidence": 1, "bounds": [127, 1071, 157, 1098]}, "text_left": {"region_type": "text_left", "main_color": "蓝色", "rgb": [92, 94, 254], "confidence": 1, "bounds": [190, 1072, 357, 1096]}, "text_right": {"region_type": "text_right", "main_color": "蓝色", "rgb": [89, 94, 255], "confidence": 1, "bounds": [357, 1072, 524, 1096]}}, "final_color": "蓝色", "confidence": 1}, {"row_index": 40, "numeric_value": "0.086", "text_elements": ["肿瘤标志物胸苷激酶"], "regions": {"digit_left": {"region_type": "digit_left", "main_color": "蓝色", "rgb": [95, 97, 255], "confidence": 1, "bounds": [98, 1096, 127, 1123]}, "digit_right": {"region_type": "digit_right", "main_color": "蓝色", "rgb": [95, 98, 255], "confidence": 1, "bounds": [127, 1096, 157, 1123]}, "text_left": {"region_type": "text_left", "main_color": "蓝色", "rgb": [90, 90, 254], "confidence": 1, "bounds": [190, 1096, 278, 1121]}, "text_right": {"region_type": "text_right", "main_color": "蓝色", "rgb": [92, 91, 255], "confidence": 1, "bounds": [278, 1096, 367, 1121]}}, "final_color": "蓝色", "confidence": 1}, {"row_index": 41, "numeric_value": "0.086", "text_elements": ["糖苷*"], "regions": {"digit_left": {"region_type": "digit_left", "main_color": "蓝色", "rgb": [93, 95, 255], "confidence": 1, "bounds": [98, 1121, 127, 1148]}, "digit_right": {"region_type": "digit_right", "main_color": "蓝色", "rgb": [93, 97, 255], "confidence": 1, "bounds": [127, 1121, 157, 1148]}, "text_left": {"region_type": "text_left", "main_color": "蓝色", "rgb": [80, 80, 253], "confidence": 1, "bounds": [188, 1121, 216, 1149]}, "text_right": {"region_type": "text_right", "main_color": "蓝色", "rgb": [83, 83, 254], "confidence": 1, "bounds": [216, 1121, 244, 1149]}}, "final_color": "蓝色", "confidence": 1}, {"row_index": 42, "numeric_value": "0.086", "text_elements": ["AST血清天冬氨酸转氨酵素SERUMASPARAGAMINOTRANSFERASE"], "regions": {"digit_left": {"region_type": "digit_left", "main_color": "蓝色", "rgb": [95, 97, 255], "confidence": 1, "bounds": [98, 1148, 127, 1174]}, "digit_right": {"region_type": "digit_right", "main_color": "蓝色", "rgb": [95, 98, 255], "confidence": 1, "bounds": [127, 1148, 157, 1174]}, "text_left": {"region_type": "text_left", "main_color": "蓝色", "rgb": [92, 93, 254], "confidence": 1, "bounds": [192, 1149, 461, 1173]}, "text_right": {"region_type": "text_right", "main_color": "蓝色", "rgb": [89, 92, 255], "confidence": 1, "bounds": [461, 1149, 731, 1173]}}, "final_color": "蓝色", "confidence": 1}, {"row_index": 43, "numeric_value": "0.086", "text_elements": ["血清中的氨基酸NITROGENOFAMINOACIDSINSERUM"], "regions": {"digit_left": {"region_type": "digit_left", "main_color": "蓝色", "rgb": [93, 95, 255], "confidence": 1, "bounds": [98, 1173, 127, 1199]}, "digit_right": {"region_type": "digit_right", "main_color": "蓝色", "rgb": [93, 97, 255], "confidence": 1, "bounds": [127, 1173, 157, 1199]}, "text_left": {"region_type": "text_left", "main_color": "蓝色", "rgb": [91, 93, 254], "confidence": 1, "bounds": [194, 1174, 418, 1198]}, "text_right": {"region_type": "text_right", "main_color": "蓝色", "rgb": [90, 94, 255], "confidence": 1, "bounds": [418, 1174, 642, 1198]}}, "final_color": "蓝色", "confidence": 1}, {"row_index": 44, "numeric_value": "0.086", "text_elements": ["抗链球菌溶血素"], "regions": {"digit_left": {"region_type": "digit_left", "main_color": "蓝色", "rgb": [95, 97, 255], "confidence": 1, "bounds": [98, 1198, 127, 1224]}, "digit_right": {"region_type": "digit_right", "main_color": "蓝色", "rgb": [95, 98, 255], "confidence": 1, "bounds": [127, 1198, 157, 1224]}, "text_left": {"region_type": "text_left", "main_color": "蓝色", "rgb": [85, 85, 254], "confidence": 1, "bounds": [190, 1199, 258, 1224]}, "text_right": {"region_type": "text_right", "main_color": "蓝色", "rgb": [88, 88, 254], "confidence": 1, "bounds": [258, 1199, 327, 1224]}}, "final_color": "蓝色", "confidence": 1}, {"row_index": 45, "numeric_value": "0.088", "text_elements": ["铁蛋白*"], "regions": {"digit_left": {"region_type": "digit_left", "main_color": "蓝色", "rgb": [93, 95, 255], "confidence": 1, "bounds": [98, 1224, 127, 1249]}, "digit_right": {"region_type": "digit_right", "main_color": "蓝色", "rgb": [92, 96, 255], "confidence": 1, "bounds": [127, 1224, 157, 1249]}, "text_left": {"region_type": "text_left", "main_color": "蓝色", "rgb": [89, 89, 254], "confidence": 1, "bounds": [186, 1221, 225, 1253]}, "text_right": {"region_type": "text_right", "main_color": "蓝色", "rgb": [83, 82, 254], "confidence": 1, "bounds": [225, 1221, 264, 1253]}}, "final_color": "蓝色", "confidence": 1}, {"row_index": 46, "numeric_value": "0.089", "text_elements": ["醛固酮血*"], "regions": {"digit_left": {"region_type": "digit_left", "main_color": "蓝色", "rgb": [95, 97, 255], "confidence": 1, "bounds": [98, 1249, 127, 1274]}, "digit_right": {"region_type": "digit_right", "main_color": "蓝色", "rgb": [95, 99, 255], "confidence": 1, "bounds": [127, 1249, 157, 1274]}, "text_left": {"region_type": "text_left", "main_color": "蓝色", "rgb": [86, 86, 253], "confidence": 1, "bounds": [190, 1249, 234, 1276]}, "text_right": {"region_type": "text_right", "main_color": "蓝色", "rgb": [88, 87, 254], "confidence": 1, "bounds": [234, 1249, 279, 1276]}}, "final_color": "蓝色", "confidence": 1}, {"row_index": 47, "numeric_value": "0.091", "text_elements": ["红细胞沉降率（ESR）"], "regions": {"digit_left": {"region_type": "digit_left", "main_color": "蓝色", "rgb": [93, 95, 255], "confidence": 1, "bounds": [98, 1274, 126, 1301]}, "digit_right": {"region_type": "digit_right", "main_color": "蓝色", "rgb": [96, 99, 255], "confidence": 1, "bounds": [126, 1274, 155, 1301]}, "text_left": {"region_type": "text_left", "main_color": "蓝色", "rgb": [93, 93, 254], "confidence": 1, "bounds": [190, 1276, 276, 1300]}, "text_right": {"region_type": "text_right", "main_color": "蓝色", "rgb": [94, 95, 255], "confidence": 1, "bounds": [276, 1276, 362, 1300]}}, "final_color": "蓝色", "confidence": 1}, {"row_index": 48, "numeric_value": "0.091", "text_elements": ["酸性磷酸酵素ACIDPHOSPHATASE"], "regions": {"digit_left": {"region_type": "digit_left", "main_color": "蓝色", "rgb": [95, 98, 255], "confidence": 1, "bounds": [98, 1301, 126, 1326]}, "digit_right": {"region_type": "digit_right", "main_color": "蓝色", "rgb": [97, 101, 255], "confidence": 1, "bounds": [126, 1301, 155, 1326]}, "text_left": {"region_type": "text_left", "main_color": "蓝色", "rgb": [90, 89, 254], "confidence": 1, "bounds": [194, 1305, 335, 1323]}, "text_right": {"region_type": "text_right", "main_color": "蓝色", "rgb": [87, 92, 255], "confidence": 1, "bounds": [335, 1305, 476, 1323]}}, "final_color": "蓝色", "confidence": 1}, {"row_index": 49, "numeric_value": "0.093", "text_elements": ["嗜中性粒细胞STABNEUTROPHILS"], "regions": {"digit_left": {"region_type": "digit_left", "main_color": "蓝色", "rgb": [93, 95, 255], "confidence": 1, "bounds": [98, 1326, 127, 1351]}, "digit_right": {"region_type": "digit_right", "main_color": "蓝色", "rgb": [95, 99, 255], "confidence": 1, "bounds": [127, 1326, 157, 1351]}, "text_left": {"region_type": "text_left", "main_color": "蓝色", "rgb": [88, 88, 254], "confidence": 1, "bounds": [194, 1330, 334, 1348]}, "text_right": {"region_type": "text_right", "main_color": "蓝色", "rgb": [89, 96, 255], "confidence": 1, "bounds": [334, 1330, 474, 1348]}}, "final_color": "蓝色", "confidence": 1}, {"row_index": 50, "numeric_value": "0.093", "text_elements": ["血尿素BLOODUREA"], "regions": {"digit_left": {"region_type": "digit_left", "main_color": "蓝色", "rgb": [95, 97, 255], "confidence": 1, "bounds": [98, 1351, 127, 1376]}, "digit_right": {"region_type": "digit_right", "main_color": "蓝色", "rgb": [95, 98, 255], "confidence": 1, "bounds": [127, 1351, 157, 1376]}, "text_left": {"region_type": "text_left", "main_color": "蓝色", "rgb": [88, 89, 254], "confidence": 1, "bounds": [192, 1351, 276, 1375]}, "text_right": {"region_type": "text_right", "main_color": "蓝色", "rgb": [87, 92, 255], "confidence": 1, "bounds": [276, 1351, 360, 1375]}}, "final_color": "蓝色", "confidence": 1}, {"row_index": 51, "numeric_value": "0.093", "text_elements": ["胆汁酸*"], "regions": {"digit_left": {"region_type": "digit_left", "main_color": "蓝色", "rgb": [93, 95, 255], "confidence": 1, "bounds": [98, 1376, 127, 1403]}, "digit_right": {"region_type": "digit_right", "main_color": "蓝色", "rgb": [95, 99, 255], "confidence": 1, "bounds": [127, 1376, 157, 1403]}, "text_left": {"region_type": "text_left", "main_color": "蓝色", "rgb": [86, 86, 254], "confidence": 1, "bounds": [188, 1370, 225, 1407]}, "text_right": {"region_type": "text_right", "main_color": "蓝色", "rgb": [95, 94, 255], "confidence": 1, "bounds": [225, 1370, 263, 1407]}}, "final_color": "蓝色", "confidence": 1}, {"row_index": 52, "numeric_value": "0.095", "text_elements": ["尿肌酥URINECREATININE"], "regions": {"digit_left": {"region_type": "digit_left", "main_color": "蓝色", "rgb": [95, 97, 255], "confidence": 1, "bounds": [100, 1403, 128, 1428]}, "digit_right": {"region_type": "digit_right", "main_color": "蓝色", "rgb": [90, 94, 255], "confidence": 1, "bounds": [128, 1403, 157, 1428]}, "text_left": {"region_type": "text_left", "main_color": "蓝色", "rgb": [89, 91, 254], "confidence": 1, "bounds": [192, 1403, 302, 1428]}, "text_right": {"region_type": "text_right", "main_color": "蓝色", "rgb": [90, 93, 255], "confidence": 1, "bounds": [302, 1403, 412, 1428]}}, "final_color": "蓝色", "confidence": 1}, {"row_index": 53, "numeric_value": "0.096", "text_elements": ["催乳素*"], "regions": {"digit_left": {"region_type": "digit_left", "main_color": "蓝色", "rgb": [93, 95, 255], "confidence": 1, "bounds": [100, 1428, 128, 1453]}, "digit_right": {"region_type": "digit_right", "main_color": "蓝色", "rgb": [94, 97, 255], "confidence": 1, "bounds": [128, 1428, 157, 1453]}, "text_left": {"region_type": "text_left", "main_color": "蓝色", "rgb": [88, 88, 254], "confidence": 1, "bounds": [190, 1428, 226, 1455]}, "text_right": {"region_type": "text_right", "main_color": "蓝色", "rgb": [94, 93, 255], "confidence": 1, "bounds": [226, 1428, 262, 1455]}}, "final_color": "蓝色", "confidence": 1}, {"row_index": 54, "numeric_value": "0.096", "text_elements": ["葡萄糖浆*"], "regions": {"digit_left": {"region_type": "digit_left", "main_color": "蓝色", "rgb": [95, 97, 255], "confidence": 1, "bounds": [98, 1453, 127, 1480]}, "digit_right": {"region_type": "digit_right", "main_color": "蓝色", "rgb": [95, 98, 255], "confidence": 1, "bounds": [127, 1453, 157, 1480]}, "text_left": {"region_type": "text_left", "main_color": "蓝色", "rgb": [84, 84, 254], "confidence": 1, "bounds": [190, 1453, 234, 1480]}, "text_right": {"region_type": "text_right", "main_color": "蓝色", "rgb": [91, 90, 254], "confidence": 1, "bounds": [234, 1453, 279, 1480]}}, "final_color": "蓝色", "confidence": 1}, {"row_index": 55, "numeric_value": "0.097", "text_elements": ["甲状腺球蛋白*"], "regions": {"digit_left": {"region_type": "digit_left", "main_color": "蓝色", "rgb": [93, 95, 255], "confidence": 1, "bounds": [98, 1478, 127, 1505]}, "digit_right": {"region_type": "digit_right", "main_color": "蓝色", "rgb": [95, 98, 255], "confidence": 1, "bounds": [127, 1478, 157, 1505]}, "text_left": {"region_type": "text_left", "main_color": "蓝色", "rgb": [87, 87, 254], "confidence": 1, "bounds": [192, 1480, 254, 1505]}, "text_right": {"region_type": "text_right", "main_color": "蓝色", "rgb": [87, 86, 255], "confidence": 1, "bounds": [254, 1480, 316, 1505]}}, "final_color": "蓝色", "confidence": 1}, {"row_index": 56, "numeric_value": "0.097", "text_elements": ["甲状腺素结合球蛋白"], "regions": {"digit_left": {"region_type": "digit_left", "main_color": "蓝色", "rgb": [95, 97, 255], "confidence": 1, "bounds": [98, 1505, 127, 1530]}, "digit_right": {"region_type": "digit_right", "main_color": "蓝色", "rgb": [92, 96, 255], "confidence": 1, "bounds": [127, 1505, 157, 1530]}, "text_left": {"region_type": "text_left", "main_color": "蓝色", "rgb": [89, 89, 254], "confidence": 1, "bounds": [192, 1505, 279, 1530]}, "text_right": {"region_type": "text_right", "main_color": "蓝色", "rgb": [87, 87, 254], "confidence": 1, "bounds": [279, 1505, 367, 1530]}}, "final_color": "蓝色", "confidence": 1}, {"row_index": 57, "numeric_value": "0.098", "text_elements": ["ALPHA2球蛋白*"], "regions": {"digit_left": {"region_type": "digit_left", "main_color": "蓝色", "rgb": [93, 95, 255], "confidence": 1, "bounds": [98, 1530, 127, 1555]}, "digit_right": {"region_type": "digit_right", "main_color": "蓝色", "rgb": [95, 99, 255], "confidence": 1, "bounds": [127, 1530, 157, 1555]}, "text_left": {"region_type": "text_left", "main_color": "蓝色", "rgb": [91, 94, 254], "confidence": 1, "bounds": [192, 1530, 258, 1555]}, "text_right": {"region_type": "text_right", "main_color": "蓝色", "rgb": [87, 87, 255], "confidence": 1, "bounds": [258, 1530, 325, 1555]}}, "final_color": "蓝色", "confidence": 1}, {"row_index": 58, "numeric_value": "0.098", "text_elements": ["嗜酸性粒细胞EOSINOPHILES"], "regions": {"digit_left": {"region_type": "digit_left", "main_color": "蓝色", "rgb": [95, 97, 255], "confidence": 1, "bounds": [98, 1555, 127, 1582]}, "digit_right": {"region_type": "digit_right", "main_color": "蓝色", "rgb": [95, 99, 255], "confidence": 1, "bounds": [127, 1555, 157, 1582]}, "text_left": {"region_type": "text_left", "main_color": "蓝色", "rgb": [89, 89, 254], "confidence": 1, "bounds": [192, 1557, 311, 1580]}, "text_right": {"region_type": "text_right", "main_color": "蓝色", "rgb": [88, 92, 255], "confidence": 1, "bounds": [311, 1557, 430, 1580]}}, "final_color": "蓝色", "confidence": 1}, {"row_index": 59, "numeric_value": "0.098", "text_elements": ["血细胞比容，全血*"], "regions": {"digit_left": {"region_type": "digit_left", "main_color": "蓝色", "rgb": [93, 95, 255], "confidence": 1, "bounds": [98, 1580, 127, 1607]}, "digit_right": {"region_type": "digit_right", "main_color": "蓝色", "rgb": [95, 99, 255], "confidence": 1, "bounds": [127, 1580, 157, 1607]}, "text_left": {"region_type": "text_left", "main_color": "蓝色", "rgb": [88, 88, 254], "confidence": 1, "bounds": [192, 1582, 272, 1607]}, "text_right": {"region_type": "text_right", "main_color": "蓝色", "rgb": [88, 88, 255], "confidence": 1, "bounds": [272, 1582, 353, 1607]}}, "final_color": "蓝色", "confidence": 1}, {"row_index": 60, "numeric_value": "0.098", "text_elements": ["血组织胺BLOODHISTAMINE"], "regions": {"digit_left": {"region_type": "digit_left", "main_color": "蓝色", "rgb": [95, 97, 255], "confidence": 1, "bounds": [98, 1607, 127, 1632]}, "digit_right": {"region_type": "digit_right", "main_color": "蓝色", "rgb": [95, 99, 255], "confidence": 1, "bounds": [127, 1607, 157, 1632]}, "text_left": {"region_type": "text_left", "main_color": "蓝色", "rgb": [87, 88, 254], "confidence": 1, "bounds": [194, 1609, 312, 1632]}, "text_right": {"region_type": "text_right", "main_color": "蓝色", "rgb": [87, 93, 255], "confidence": 1, "bounds": [312, 1609, 430, 1632]}}, "final_color": "蓝色", "confidence": 1}, {"row_index": 61, "numeric_value": "0.099", "text_elements": ["维生素B1（THIAMINE）*"], "regions": {"digit_left": {"region_type": "digit_left", "main_color": "蓝色", "rgb": [93, 95, 255], "confidence": 1, "bounds": [100, 1632, 128, 1657]}, "digit_right": {"region_type": "digit_right", "main_color": "蓝色", "rgb": [93, 95, 255], "confidence": 1, "bounds": [128, 1632, 157, 1657]}, "text_left": {"region_type": "text_left", "main_color": "蓝色", "rgb": [88, 88, 254], "confidence": 1, "bounds": [192, 1634, 297, 1657]}, "text_right": {"region_type": "text_right", "main_color": "蓝色", "rgb": [92, 97, 255], "confidence": 1, "bounds": [297, 1634, 402, 1657]}}, "final_color": "蓝色", "confidence": 1}, {"row_index": 62, "numeric_value": "0.099", "text_elements": ["糖基化血红蛋白"], "regions": {"digit_left": {"region_type": "digit_left", "main_color": "蓝色", "rgb": [95, 97, 255], "confidence": 1, "bounds": [100, 1657, 128, 1684]}, "digit_right": {"region_type": "digit_right", "main_color": "蓝色", "rgb": [93, 96, 255], "confidence": 1, "bounds": [128, 1657, 157, 1684]}, "text_left": {"region_type": "text_left", "main_color": "蓝色", "rgb": [85, 85, 254], "confidence": 1, "bounds": [192, 1659, 261, 1684]}, "text_right": {"region_type": "text_right", "main_color": "蓝色", "rgb": [85, 84, 254], "confidence": 1, "bounds": [261, 1659, 330, 1684]}}, "final_color": "蓝色", "confidence": 1}, {"row_index": 63, "numeric_value": "0.099", "text_elements": ["胰高血糖素*"], "regions": {"digit_left": {"region_type": "digit_left", "main_color": "蓝色", "rgb": [93, 95, 255], "confidence": 1, "bounds": [100, 1682, 128, 1709]}, "digit_right": {"region_type": "digit_right", "main_color": "蓝色", "rgb": [93, 95, 255], "confidence": 1, "bounds": [128, 1682, 157, 1709]}, "text_left": {"region_type": "text_left", "main_color": "蓝色", "rgb": [93, 93, 254], "confidence": 1, "bounds": [190, 1682, 243, 1712]}, "text_right": {"region_type": "text_right", "main_color": "蓝色", "rgb": [87, 86, 254], "confidence": 1, "bounds": [243, 1682, 297, 1712]}}, "final_color": "蓝色", "confidence": 1}]}