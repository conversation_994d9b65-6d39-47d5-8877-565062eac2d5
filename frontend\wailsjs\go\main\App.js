// @ts-check
// Cynhyrchwyd y ffeil hon yn awtomatig. PEIDIWCH Â MODIWL
// This file is automatically generated. DO NOT EDIT

export function AddPatient(arg1) {
  return window['go']['main']['App']['AddPatient'](arg1);
}

export function AutoCollapseAfterInactivity() {
  return window['go']['main']['App']['AutoCollapseAfterInactivity']();
}

export function ClearCurrentPatient() {
  return window['go']['main']['App']['ClearCurrentPatient']();
}

export function ClearPatientList() {
  return window['go']['main']['App']['ClearPatientList']();
}

export function ExportCurrentUserCheckingInfoJSON(arg1) {
  return window['go']['main']['App']['ExportCurrentUserCheckingInfoJSON'](arg1);
}

export function ExtractOrganFromScreenshot(arg1) {
  return window['go']['main']['App']['ExtractOrganFromScreenshot'](arg1);
}

export function GenerateQRCode() {
  return window['go']['main']['App']['GenerateQRCode']();
}

export function GenerateRegistrationQRCode() {
  return window['go']['main']['App']['GenerateRegistrationQRCode']();
}

export function GetCompletedPatients() {
  return window['go']['main']['App']['GetCompletedPatients']();
}

export function GetCompletedPatientsByDate(arg1) {
  return window['go']['main']['App']['GetCompletedPatientsByDate'](arg1);
}

export function GetConfig() {
  return window['go']['main']['App']['GetConfig']();
}

export function GetCurrentPatientIndex() {
  return window['go']['main']['App']['GetCurrentPatientIndex']();
}

export function GetCurrentRegistrationNumber() {
  return window['go']['main']['App']['GetCurrentRegistrationNumber']();
}

export function GetCurrentUserCheckingInfo(arg1) {
  return window['go']['main']['App']['GetCurrentUserCheckingInfo'](arg1);
}

export function GetModeConfig() {
  return window['go']['main']['App']['GetModeConfig']();
}

export function GetPatientList() {
  return window['go']['main']['App']['GetPatientList']();
}

export function GetPendingPatients(arg1) {
  return window['go']['main']['App']['GetPendingPatients'](arg1);
}

export function GetPendingRegistrations() {
  return window['go']['main']['App']['GetPendingRegistrations']();
}

export function GetRegistrations(arg1) {
  return window['go']['main']['App']['GetRegistrations'](arg1);
}

export function GetSiteInfo() {
  return window['go']['main']['App']['GetSiteInfo']();
}

export function GetTodayPatientCount() {
  return window['go']['main']['App']['GetTodayPatientCount']();
}

export function GetUnanalyzedPatients(arg1) {
  return window['go']['main']['App']['GetUnanalyzedPatients'](arg1);
}

export function GetWindowState() {
  return window['go']['main']['App']['GetWindowState']();
}

export function Greet(arg1) {
  return window['go']['main']['App']['Greet'](arg1);
}

export function HandleCozeLLM_AnylizeD_value(arg1) {
  return window['go']['main']['App']['HandleCozeLLM_AnylizeD_value'](arg1);
}

export function HandleHotkey(arg1) {
  return window['go']['main']['App']['HandleHotkey'](arg1);
}

export function HandleKeyboardShortcut(arg1) {
  return window['go']['main']['App']['HandleKeyboardShortcut'](arg1);
}

export function MarkPatientCompleted(arg1, arg2) {
  return window['go']['main']['App']['MarkPatientCompleted'](arg1, arg2);
}

export function MinimizeWindow() {
  return window['go']['main']['App']['MinimizeWindow']();
}

export function MoveToNextPatient() {
  return window['go']['main']['App']['MoveToNextPatient']();
}

export function ProcessImageWithOCR(arg1) {
  return window['go']['main']['App']['ProcessImageWithOCR'](arg1);
}

export function ProcessScreenshotWithOCR(arg1, arg2) {
  return window['go']['main']['App']['ProcessScreenshotWithOCR'](arg1, arg2);
}

export function ProcessScreenshotWorkflow(arg1) {
  return window['go']['main']['App']['ProcessScreenshotWorkflow'](arg1);
}

export function RemovePatient(arg1) {
  return window['go']['main']['App']['RemovePatient'](arg1);
}

export function SetAlwaysOnTop(arg1) {
  return window['go']['main']['App']['SetAlwaysOnTop'](arg1);
}

export function SetCompactWindow() {
  return window['go']['main']['App']['SetCompactWindow']();
}

export function SetCurrentPatientIndex(arg1) {
  return window['go']['main']['App']['SetCurrentPatientIndex'](arg1);
}

export function SetCurrentPatientName(arg1) {
  return window['go']['main']['App']['SetCurrentPatientName'](arg1);
}

export function SetExpandedWindow() {
  return window['go']['main']['App']['SetExpandedWindow']();
}

export function SetWindowPosition(arg1) {
  return window['go']['main']['App']['SetWindowPosition'](arg1);
}

export function ShowErrorNotification(arg1, arg2, arg3) {
  return window['go']['main']['App']['ShowErrorNotification'](arg1, arg2, arg3);
}

export function ShowInfoNotification(arg1, arg2, arg3) {
  return window['go']['main']['App']['ShowInfoNotification'](arg1, arg2, arg3);
}

export function ShowOCRProcessNotification(arg1, arg2, arg3) {
  return window['go']['main']['App']['ShowOCRProcessNotification'](arg1, arg2, arg3);
}

export function ShowProgressNotification(arg1, arg2, arg3, arg4) {
  return window['go']['main']['App']['ShowProgressNotification'](arg1, arg2, arg3, arg4);
}

export function ShowSuccessNotification(arg1, arg2, arg3) {
  return window['go']['main']['App']['ShowSuccessNotification'](arg1, arg2, arg3);
}

export function ShowToastNotification(arg1, arg2, arg3, arg4, arg5, arg6) {
  return window['go']['main']['App']['ShowToastNotification'](arg1, arg2, arg3, arg4, arg5, arg6);
}

export function ShowWailsNotification(arg1, arg2, arg3, arg4) {
  return window['go']['main']['App']['ShowWailsNotification'](arg1, arg2, arg3, arg4);
}

export function ShowWarningNotification(arg1, arg2, arg3) {
  return window['go']['main']['App']['ShowWarningNotification'](arg1, arg2, arg3);
}

export function TakeConcurrentScreenshot(arg1, arg2) {
  return window['go']['main']['App']['TakeConcurrentScreenshot'](arg1, arg2);
}

export function TakeOCRScreenshot(arg1, arg2) {
  return window['go']['main']['App']['TakeOCRScreenshot'](arg1, arg2);
}

export function TestOCR(arg1) {
  return window['go']['main']['App']['TestOCR'](arg1);
}

export function ToggleWindowSize() {
  return window['go']['main']['App']['ToggleWindowSize']();
}

export function UpdateCropSettings(arg1) {
  return window['go']['main']['App']['UpdateCropSettings'](arg1);
}

export function UpdateNotificationMode(arg1) {
  return window['go']['main']['App']['UpdateNotificationMode'](arg1);
}

export function UpdateProgressNotification(arg1, arg2, arg3) {
  return window['go']['main']['App']['UpdateProgressNotification'](arg1, arg2, arg3);
}

export function UpdateSiteInfo(arg1) {
  return window['go']['main']['App']['UpdateSiteInfo'](arg1);
}

export function ValidateOCRSetup() {
  return window['go']['main']['App']['ValidateOCRSetup']();
}
