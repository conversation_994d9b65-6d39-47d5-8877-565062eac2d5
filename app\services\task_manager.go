package services

import (
	"context"
	"fmt"
	"sync"
	"time"

	"MagneticOperator/app/utils"

	"github.com/wailsapp/wails/v2/pkg/runtime"
	"go.uber.org/zap"
)

// TaskType 任务类型枚举
type TaskType string

const (
	TaskTypeScreenshotA TaskType = "screenshot_A" // 器官问题来源分析
	TaskTypeScreenshotB TaskType = "screenshot_B" // 生化平衡分析
	TaskTypeScreenshotC TaskType = "screenshot_C" // 病理形态学分析
	TaskTypeOCR         TaskType = "ocr_process"  // OCR处理
	TaskTypeUpload      TaskType = "upload"       // 上传任务
)

// TaskStatus 任务状态枚举
type TaskStatus string

const (
	TaskStatusPending   TaskStatus = "pending"   // 等待中
	TaskStatusRunning   TaskStatus = "running"   // 执行中
	TaskStatusCompleted TaskStatus = "completed" // 已完成
	TaskStatusFailed    TaskStatus = "failed"    // 失败
	TaskStatusCancelled TaskStatus = "cancelled" // 已取消
)

// TaskPriority 任务优先级
type TaskPriority int

const (
	PriorityLow    TaskPriority = 1
	PriorityNormal TaskPriority = 2
	PriorityHigh   TaskPriority = 3
	PriorityUrgent TaskPriority = 4
)

// Task 任务结构体
type Task struct {
	ID          string                 `json:"id"`           // 任务ID
	Type        TaskType               `json:"type"`         // 任务类型
	Status      TaskStatus             `json:"status"`       // 任务状态
	Priority    TaskPriority           `json:"priority"`     // 任务优先级
	UserName    string                 `json:"user_name"`    // 用户名
	Mode        string                 `json:"mode"`         // 模式（A/B/C）
	RoundNumber int                    `json:"round_number"` // 轮次编号
	CreatedAt   time.Time              `json:"created_at"`   // 创建时间
	StartedAt   *time.Time             `json:"started_at"`   // 开始时间
	CompletedAt *time.Time             `json:"completed_at"` // 完成时间
	Error       error                  `json:"error"`        // 错误信息
	Result      interface{}            `json:"result"`       // 任务结果
	Context     context.Context        `json:"-"`            // 任务上下文
	Cancel      context.CancelFunc     `json:"-"`            // 取消函数
	Handler     TaskHandler            `json:"-"`            // 任务处理器
	Metadata    map[string]interface{} `json:"metadata"`     // 元数据
}

// TaskHandler 任务处理器接口
type TaskHandler interface {
	Execute(ctx context.Context, task *Task) (interface{}, error)
	GetType() TaskType
	GetTimeout() time.Duration
}

// TaskResult 任务结果
type TaskResult struct {
	TaskID      string      `json:"task_id"`
	Status      TaskStatus  `json:"status"`
	Result      interface{} `json:"result"`
	Error       string      `json:"error,omitempty"`
	Duration    int64       `json:"duration"` // 执行时长（毫秒）
	CompletedAt time.Time   `json:"completed_at"`
}

// TaskManagerConfig 任务管理器配置
type TaskManagerConfig struct {
	MaxConcurrentTasks   int           `json:"max_concurrent_tasks"`   // 最大并发任务数
	MaxQueueSize         int           `json:"max_queue_size"`         // 最大队列大小
	TaskTimeout          time.Duration `json:"task_timeout"`           // 任务超时时间
	CleanupInterval      time.Duration `json:"cleanup_interval"`       // 清理间隔
	RetryAttempts        int           `json:"retry_attempts"`         // 重试次数
	RetryDelay           time.Duration `json:"retry_delay"`            // 重试延迟
	EnableDuplicateCheck bool          `json:"enable_duplicate_check"` // 启用重复检查
	CooldownPeriod       time.Duration `json:"cooldown_period"`        // 冷却期
}

// DefaultTaskManagerConfig 默认配置
func DefaultTaskManagerConfig() *TaskManagerConfig {
	return &TaskManagerConfig{
		MaxConcurrentTasks:   3,  // 最多3个并发任务
		MaxQueueSize:         10, // 队列最多10个任务
		TaskTimeout:          30 * time.Second,
		CleanupInterval:      5 * time.Minute,
		RetryAttempts:        2,
		RetryDelay:           1 * time.Second,
		EnableDuplicateCheck: true,
		CooldownPeriod:       500 * time.Millisecond, // 0.5秒冷却期，允许快速切换B/C模式
	}
}

// TaskManager 任务管理器
type TaskManager struct {
	config         *TaskManagerConfig
	ctx            context.Context
	cancel         context.CancelFunc
	taskQueue      chan *Task
	activeTasks    map[string]*Task
	completedTasks map[string]*TaskResult
	handlers       map[TaskType]TaskHandler
	lastTaskTime   map[string]time.Time // 记录每种任务的最后执行时间（格式：taskType_userName_roundNumber）
	mu             sync.RWMutex
	wg             sync.WaitGroup
	running        bool
	stats          *TaskManagerStats
	eventEmitter   EventEmitter // 事件发射器接口
}

// TaskManagerStats 任务管理器统计信息
type TaskManagerStats struct {
	TotalSubmitted  int64         `json:"total_submitted"`
	TotalCompleted  int64         `json:"total_completed"`
	TotalFailed     int64         `json:"total_failed"`
	TotalCancelled  int64         `json:"total_cancelled"`
	ActiveTasks     int           `json:"active_tasks"`
	QueuedTasks     int           `json:"queued_tasks"`
	AverageExecTime time.Duration `json:"average_exec_time"`
	StartTime       time.Time     `json:"start_time"`
	mu              sync.RWMutex  `json:"-"`
}

// EventEmitter 事件发射器接口
type EventEmitter interface {
	EmitEvent(eventName string, data interface{})
}

// WailsEventEmitter Wails事件发射器实现
type WailsEventEmitter struct {
	ctx context.Context
	app AppNotifier // 添加App通知接口
}

// AppNotifier App通知接口
type AppNotifier interface {
	SendTaskCompletedNotification(taskResult *TaskResult)
}

func NewWailsEventEmitter(ctx context.Context) *WailsEventEmitter {
	return &WailsEventEmitter{ctx: ctx}
}

// SetAppNotifier 设置App通知器
func (w *WailsEventEmitter) SetAppNotifier(app AppNotifier) {
	w.app = app
}

func (w *WailsEventEmitter) EmitEvent(eventName string, data interface{}) {
	if w.ctx != nil {
		runtime.EventsEmit(w.ctx, eventName, data)
	}

	// 特殊处理任务完成事件
	if eventName == "task-completed" && w.app != nil {
		if taskResult, ok := data.(*TaskResult); ok {
			w.app.SendTaskCompletedNotification(taskResult)
		}
	}
}

// NewTaskManager 创建新的任务管理器
func NewTaskManager(config *TaskManagerConfig, eventEmitter EventEmitter) *TaskManager {
	if config == nil {
		config = DefaultTaskManagerConfig()
	}

	ctx, cancel := context.WithCancel(context.Background())

	tm := &TaskManager{
		config:         config,
		ctx:            ctx,
		cancel:         cancel,
		taskQueue:      make(chan *Task, config.MaxQueueSize),
		activeTasks:    make(map[string]*Task),
		completedTasks: make(map[string]*TaskResult),
		handlers:       make(map[TaskType]TaskHandler),
		lastTaskTime:   make(map[string]time.Time),
		running:        false,
		eventEmitter:   eventEmitter,
		stats: &TaskManagerStats{
			StartTime: time.Now(),
		},
	}

	return tm
}

// RegisterHandler 注册任务处理器
func (tm *TaskManager) RegisterHandler(handler TaskHandler) error {
	tm.mu.Lock()
	defer tm.mu.Unlock()

	taskType := handler.GetType()
	if _, exists := tm.handlers[taskType]; exists {
		return fmt.Errorf("handler for task type %s already registered", taskType)
	}

	tm.handlers[taskType] = handler
	utils.LogInfo("任务处理器注册成功", zap.String("taskType", string(taskType)))
	return nil
}

// Start 启动任务管理器
func (tm *TaskManager) Start() error {
	tm.mu.Lock()
	defer tm.mu.Unlock()

	if tm.running {
		utils.LogWarning("任务管理器已经在运行中")
		return fmt.Errorf("task manager is already running")
	}

	tm.running = true
	utils.LogInfo("任务管理器启动成功",
		zap.Int("maxConcurrent", tm.config.MaxConcurrentTasks),
		zap.Int("registeredHandlers", len(tm.handlers)),
		zap.Duration("cooldownPeriod", tm.config.CooldownPeriod))

	// 启动工作协程
	utils.LogInfo("启动工作协程", zap.Int("workerCount", tm.config.MaxConcurrentTasks))
	for i := 0; i < tm.config.MaxConcurrentTasks; i++ {
		tm.wg.Add(1)
		go tm.worker(i)
		utils.LogInfo("工作协程启动", zap.Int("workerId", i))
	}

	// 启动清理协程
	tm.wg.Add(1)
	go tm.cleaner()
	utils.LogInfo("清理协程启动成功")

	// 发送启动事件
	if tm.eventEmitter != nil {
		tm.eventEmitter.EmitEvent("task-manager-started", map[string]interface{}{
			"max_concurrent": tm.config.MaxConcurrentTasks,
			"max_queue_size": tm.config.MaxQueueSize,
		})
		utils.LogInfo("任务管理器启动事件已发送")
	}

	return nil
}

// Stop 停止任务管理器
func (tm *TaskManager) Stop() error {
	tm.mu.Lock()
	if !tm.running {
		tm.mu.Unlock()
		return fmt.Errorf("task manager is not running")
	}
	tm.running = false
	tm.mu.Unlock()

	// 取消所有任务
	tm.cancel()

	// 等待所有工作协程结束
	tm.wg.Wait()

	utils.LogInfo("任务管理器已停止")

	// 发送停止事件
	if tm.eventEmitter != nil {
		tm.eventEmitter.EmitEvent("task-manager-stopped", tm.GetStats())
	}

	return nil
}

// SubmitTask 提交任务
func (tm *TaskManager) SubmitTask(taskType TaskType, mode, userName string, roundNumber int, priority TaskPriority, metadata map[string]interface{}) (*Task, error) {
	utils.LogInfo("收到任务提交请求",
		zap.String("taskType", string(taskType)),
		zap.String("mode", mode),
		zap.String("userName", userName),
		zap.Int("roundNumber", roundNumber),
		zap.String("priority", string(priority)))

	tm.mu.Lock()
	defer tm.mu.Unlock()

	if !tm.running {
		utils.LogError("任务管理器未运行，拒绝任务提交", "", fmt.Errorf("task manager is not running"))
		return nil, fmt.Errorf("task manager is not running")
	}

	// 检查处理器是否存在
	handler, exists := tm.handlers[taskType]
	if !exists {
		utils.LogError("未找到任务处理器", "", fmt.Errorf("no handler registered for task type: %s", taskType))
		return nil, fmt.Errorf("no handler registered for task type: %s", taskType)
	}

	utils.LogInfo("任务处理器检查通过", zap.String("taskType", string(taskType)))

	// 检查冷却期 - 使用更精确的键来避免B和C模式互相阻塞
	if tm.config.EnableDuplicateCheck {
		cooldownKey := fmt.Sprintf("%s_%s_%d", taskType, userName, roundNumber)
		if lastTime, exists := tm.lastTaskTime[cooldownKey]; exists {
			if time.Since(lastTime) < tm.config.CooldownPeriod {
				return nil, fmt.Errorf("task %s for user %s round %d is in cooldown period, please wait %v",
					taskType, userName, roundNumber, tm.config.CooldownPeriod-time.Since(lastTime))
			}
		}
	}

	// 检查是否有相同类型的任务正在执行
	// 注意：B模式和C模式应该可以并发执行，只检查完全相同的任务类型+用户+轮次组合
	if tm.config.EnableDuplicateCheck {
		taskKey := fmt.Sprintf("%s_%s_%d", taskType, userName, roundNumber)
		for _, activeTask := range tm.activeTasks {
			activeTaskKey := fmt.Sprintf("%s_%s_%d", activeTask.Type, activeTask.UserName, activeTask.RoundNumber)
			if activeTaskKey == taskKey && activeTask.Status == TaskStatusRunning {
				return nil, fmt.Errorf("task %s for user %s round %d is already running", taskType, userName, roundNumber)
			}
		}
	}

	// 创建任务上下文
	taskCtx, taskCancel := context.WithTimeout(tm.ctx, handler.GetTimeout())

	// 创建任务
	task := &Task{
		ID:          fmt.Sprintf("%s_%d_%s", taskType, time.Now().UnixNano(), userName),
		Type:        taskType,
		Status:      TaskStatusPending,
		Priority:    priority,
		UserName:    userName,
		Mode:        mode,
		RoundNumber: roundNumber,
		CreatedAt:   time.Now(),
		Context:     taskCtx,
		Cancel:      taskCancel,
		Handler:     handler,
		Metadata:    metadata,
	}

	utils.LogInfo("任务创建成功",
		zap.String("taskID", task.ID),
		zap.String("taskType", string(taskType)),
		zap.String("mode", mode),
		zap.String("userName", userName),
		zap.Int("roundNumber", roundNumber),
		zap.Duration("timeout", handler.GetTimeout()))

	// 尝试提交到队列
	select {
	case tm.taskQueue <- task:
		tm.stats.mu.Lock()
		tm.stats.TotalSubmitted++
		tm.stats.QueuedTasks++
		tm.stats.mu.Unlock()

		// 更新最后执行时间
		cooldownKey := fmt.Sprintf("%s_%s_%d", taskType, userName, roundNumber)
		tm.lastTaskTime[cooldownKey] = time.Now()

		utils.LogInfo("任务提交成功",
			zap.String("taskID", task.ID),
			zap.String("taskType", string(taskType)),
			zap.String("userName", userName),
			zap.Int("priority", int(priority)))

		// 发送任务提交事件
		if tm.eventEmitter != nil {
			tm.eventEmitter.EmitEvent("task-submitted", map[string]interface{}{
				"task_id":   task.ID,
				"task_type": string(taskType),
				"user_name": userName,
				"priority":  int(priority),
			})
		}

		return task, nil
	default:
		taskCancel() // 取消任务上下文
		utils.LogError(fmt.Sprintf("任务队列已满，无法提交任务 - 类型: %s, 用户: %s, 队列大小: %d/%d",
			string(taskType), userName, len(tm.taskQueue), tm.config.MaxQueueSize), "",
			fmt.Errorf("task queue is full, cannot submit task"))
		return nil, fmt.Errorf("task queue is full, cannot submit task")
	}
}

// worker 工作协程
func (tm *TaskManager) worker(workerID int) {
	defer tm.wg.Done()

	utils.LogInfo("任务工作协程启动", zap.Int("workerID", workerID))

	for {
		select {
		case <-tm.ctx.Done():
			utils.LogInfo("任务工作协程停止", zap.Int("workerID", workerID))
			return
		case task := <-tm.taskQueue:
			tm.processTask(task, workerID)
		}
	}
}

// processTask 处理任务
func (tm *TaskManager) processTask(task *Task, workerID int) {
	startTime := time.Now()

	utils.LogInfo("开始处理任务",
		zap.Int("workerID", workerID),
		zap.String("taskID", task.ID),
		zap.String("taskType", string(task.Type)),
		zap.String("userName", task.UserName),
		zap.String("mode", task.Mode),
		zap.Int("roundNumber", task.RoundNumber))

	// 更新任务状态为运行中
	tm.mu.Lock()
	task.Status = TaskStatusRunning
	task.StartedAt = &startTime
	tm.activeTasks[task.ID] = task
	tm.stats.mu.Lock()
	tm.stats.QueuedTasks--
	tm.stats.ActiveTasks++
	tm.stats.mu.Unlock()
	tm.mu.Unlock()

	utils.LogInfo("开始处理任务",
		zap.String("taskID", task.ID),
		zap.String("taskType", string(task.Type)),
		zap.Int("workerID", workerID))

	// 发送任务开始事件
	if tm.eventEmitter != nil {
		tm.eventEmitter.EmitEvent("task-started", map[string]interface{}{
			"task_id":   task.ID,
			"task_type": string(task.Type),
			"worker_id": workerID,
		})
	}

	// 执行任务
	var result interface{}
	var err error

	// 使用重试机制执行任务
	for attempt := 0; attempt <= tm.config.RetryAttempts; attempt++ {
		if attempt > 0 {
			utils.LogInfo("任务重试",
				zap.String("taskID", task.ID),
				zap.Int("attempt", attempt))

			// 重试延迟
			select {
			case <-task.Context.Done():
				err = task.Context.Err()
				break
			case <-time.After(tm.config.RetryDelay):
			}
		}

		utils.LogInfo("执行任务处理器",
			zap.String("taskID", task.ID),
			zap.Int("attempt", attempt+1))

		result, err = task.Handler.Execute(task.Context, task)
		if err == nil {
			utils.LogInfo("任务执行成功",
				zap.String("taskID", task.ID),
				zap.Int("attempt", attempt+1))
			break // 成功执行，跳出重试循环
		}

		if attempt < tm.config.RetryAttempts {
			utils.LogWarning("任务执行失败，准备重试",
				zap.String("taskID", task.ID),
				zap.Int("attempt", attempt+1),
				zap.Error(err))
		} else {
			utils.LogError(fmt.Sprintf("任务执行最终失败 - 任务ID: %s, 总尝试次数: %d",
				task.ID, attempt+1), task.UserName, err)
		}
	}

	// 计算执行时长
	duration := time.Since(startTime)
	completedAt := time.Now()

	// 更新任务状态
	tm.mu.Lock()
	delete(tm.activeTasks, task.ID)

	var taskResult *TaskResult
	if err != nil {
		task.Status = TaskStatusFailed
		task.Error = err
		taskResult = &TaskResult{
			TaskID:      task.ID,
			Status:      TaskStatusFailed,
			Error:       err.Error(),
			Duration:    duration.Milliseconds(),
			CompletedAt: completedAt,
		}
		tm.stats.mu.Lock()
		tm.stats.TotalFailed++
		tm.stats.mu.Unlock()

		utils.LogError(fmt.Sprintf("任务执行失败 - 任务ID: %s, 耗时: %v",
			task.ID, duration), task.UserName, err)
	} else {
		task.Status = TaskStatusCompleted
		task.Result = result
		task.CompletedAt = &completedAt
		taskResult = &TaskResult{
			TaskID:      task.ID,
			Status:      TaskStatusCompleted,
			Result:      result,
			Duration:    duration.Milliseconds(),
			CompletedAt: completedAt,
		}
		tm.stats.mu.Lock()
		tm.stats.TotalCompleted++
		tm.stats.mu.Unlock()

		utils.LogInfo("任务执行完成",
			zap.String("taskID", task.ID),
			zap.String("taskType", string(task.Type)),
			zap.String("userName", task.UserName),
			zap.Duration("duration", duration),
			zap.Int("workerID", workerID))
	}

	tm.completedTasks[task.ID] = taskResult
	tm.stats.mu.Lock()
	tm.stats.ActiveTasks--
	tm.stats.mu.Unlock()
	tm.mu.Unlock()

	// 清理任务上下文
	task.Cancel()

	if err != nil {
		utils.LogError(fmt.Sprintf("任务执行失败 - TaskID: %s, Type: %s, Worker: %d, Duration: %v",
			task.ID, task.Type, workerID, duration), task.UserName, err)
	} else {
		utils.LogInfo(fmt.Sprintf("任务执行成功 - TaskID: %s, Type: %s, Worker: %d, Duration: %v",
			task.ID, task.Type, workerID, duration))
	}

	// 发送任务完成事件
	if tm.eventEmitter != nil {
		utils.LogInfo("发送任务完成事件",
			zap.String("taskID", task.ID),
			zap.String("status", string(taskResult.Status)))
		tm.eventEmitter.EmitEvent("task-completed", taskResult)
	} else {
		utils.LogWarning("事件发射器为nil，无法发送任务完成事件",
			zap.String("taskID", task.ID))
	}
}

// cleaner 清理协程
func (tm *TaskManager) cleaner() {
	defer tm.wg.Done()

	ticker := time.NewTicker(tm.config.CleanupInterval)
	defer ticker.Stop()

	for {
		select {
		case <-tm.ctx.Done():
			return
		case <-ticker.C:
			tm.cleanupCompletedTasks()
		}
	}
}

// cleanupCompletedTasks 清理已完成的任务
func (tm *TaskManager) cleanupCompletedTasks() {
	tm.mu.Lock()
	defer tm.mu.Unlock()

	// 保留最近1小时的任务结果
	cutoffTime := time.Now().Add(-1 * time.Hour)

	for taskID, result := range tm.completedTasks {
		if result.CompletedAt.Before(cutoffTime) {
			delete(tm.completedTasks, taskID)
		}
	}

	utils.LogInfo("清理已完成任务", zap.Int("remaining", len(tm.completedTasks)))
}

// GetStats 获取统计信息
func (tm *TaskManager) GetStats() *TaskManagerStats {
	tm.stats.mu.RLock()
	defer tm.stats.mu.RUnlock()

	// 创建副本以避免并发访问问题
	stats := &TaskManagerStats{
		TotalSubmitted:  tm.stats.TotalSubmitted,
		TotalCompleted:  tm.stats.TotalCompleted,
		TotalFailed:     tm.stats.TotalFailed,
		TotalCancelled:  tm.stats.TotalCancelled,
		ActiveTasks:     tm.stats.ActiveTasks,
		QueuedTasks:     tm.stats.QueuedTasks,
		AverageExecTime: tm.stats.AverageExecTime,
		StartTime:       tm.stats.StartTime,
	}

	return stats
}

// GetTask 获取任务信息
func (tm *TaskManager) GetTask(taskID string) (*Task, bool) {
	tm.mu.RLock()
	defer tm.mu.RUnlock()

	if task, exists := tm.activeTasks[taskID]; exists {
		return task, true
	}

	return nil, false
}

// GetTaskResult 获取任务结果
func (tm *TaskManager) GetTaskResult(taskID string) (*TaskResult, bool) {
	tm.mu.RLock()
	defer tm.mu.RUnlock()

	result, exists := tm.completedTasks[taskID]
	return result, exists
}

// CancelTask 取消任务
func (tm *TaskManager) CancelTask(taskID string) error {
	tm.mu.Lock()
	defer tm.mu.Unlock()

	task, exists := tm.activeTasks[taskID]
	if !exists {
		return fmt.Errorf("task %s not found or not active", taskID)
	}

	// 取消任务上下文
	task.Cancel()
	task.Status = TaskStatusCancelled

	// 从活跃任务中移除
	delete(tm.activeTasks, taskID)

	// 添加到已完成任务
	result := &TaskResult{
		TaskID:      taskID,
		Status:      TaskStatusCancelled,
		CompletedAt: time.Now(),
	}
	tm.completedTasks[taskID] = result

	// 更新统计
	tm.stats.mu.Lock()
	tm.stats.ActiveTasks--
	tm.stats.TotalCancelled++
	tm.stats.mu.Unlock()

	utils.LogInfo("任务已取消", zap.String("taskID", taskID))

	// 发送取消事件
	if tm.eventEmitter != nil {
		tm.eventEmitter.EmitEvent("task-cancelled", result)
	}

	return nil
}

// IsRunning 检查任务管理器是否运行中
func (tm *TaskManager) IsRunning() bool {
	tm.mu.RLock()
	defer tm.mu.RUnlock()
	return tm.running
}

// GetQueueSize 获取队列大小
func (tm *TaskManager) GetQueueSize() int {
	return len(tm.taskQueue)
}

// GetActiveTaskCount 获取活跃任务数量
func (tm *TaskManager) GetActiveTaskCount() int {
	tm.mu.RLock()
	defer tm.mu.RUnlock()
	return len(tm.activeTasks)
}
