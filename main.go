package main

import (
	"embed"
	"fmt"
	"log"
	"os"
	"os/exec"
	"os/signal"
	"path/filepath"
	"strconv"
	"strings"
	"syscall"
	"time"

	"MagneticOperator/app/utils"

	"github.com/wailsapp/wails/v2"
	"github.com/wailsapp/wails/v2/pkg/options"
	"github.com/wailsapp/wails/v2/pkg/options/assetserver"

	"go.uber.org/zap"
	"go.uber.org/zap/zapcore"
)

//go:embed all:frontend/dist
var assets embed.FS

// 全局变量存储锁文件路径
var lockFilePath string
var lockFile *os.File

// checkSingleInstance checks if another instance is already running.
func checkSingleInstance() bool {
	utils.LogInfo("检查现有实例...")

	exePath, err := os.Executable()
	if err != nil {
		utils.LogError("获取可执行文件路径失败", "", err)
		return false
	}
	exeDir := filepath.Dir(exePath)
	lockFilePath = filepath.Join(exeDir, "MagneticOperator.lock")
	utils.LogInfo("锁文件路径", zap.String("path", lockFilePath))

	if info, err := os.Stat(lockFilePath); err == nil {
		utils.LogInfo("发现现有锁文件", zap.Int64("size", info.Size()), zap.Time("modified", info.ModTime()))

		data, err := os.ReadFile(lockFilePath)
		if err != nil {
			utils.LogWarning("读取锁文件失败", zap.Error(err))
		} else {
			pidStr := strings.TrimSpace(string(data))
			utils.LogInfo("锁文件内容", zap.String("pid", pidStr))

			if pid, err := strconv.Atoi(pidStr); err == nil {
				utils.LogInfo("检查进程是否运行", zap.Int("pid", pid))
				if isProcessRunning(pid) {
					utils.LogWarning("进程仍在运行，拒绝启动新实例", zap.Int("pid", pid))
					return false
				} else {
					utils.LogInfo("进程未找到，清理旧锁文件", zap.Int("pid", pid))
				}
			} else {
				utils.LogWarning("无效的锁文件内容", zap.Error(err))
			}
		}

		if err := os.Remove(lockFilePath); err != nil {
			utils.LogWarning("删除旧锁文件失败", zap.Error(err))
		} else {
			utils.LogInfo("成功删除旧锁文件")
		}
	} else {
		utils.LogInfo("未找到现有锁文件")
	}

	utils.LogInfo("尝试创建新锁文件...")
	lockFile, err = os.OpenFile(lockFilePath, os.O_CREATE|os.O_WRONLY|os.O_EXCL, 0666)
	if err != nil {
		utils.LogError("创建锁文件失败", "", err)
		time.Sleep(100 * time.Millisecond)
		if data, err := os.ReadFile(lockFilePath); err == nil {
			if pid, err := strconv.Atoi(strings.TrimSpace(string(data))); err == nil {
				if isProcessRunning(pid) {
					utils.LogWarning("另一个实例已启动", zap.Int("pid", pid))
					return false
				}
			}
		}
		return false
	}

	pid := os.Getpid()
	utils.LogInfo("写入当前PID到锁文件", zap.Int("pid", pid))
	if _, err := lockFile.WriteString(fmt.Sprintf("%d", pid)); err != nil {
		utils.LogError("写入PID到锁文件失败", "", err)
		lockFile.Close()
		os.Remove(lockFilePath)
		return false
	}

	if err := lockFile.Sync(); err != nil {
		utils.LogWarning("同步锁文件失败", zap.Error(err))
	}

	utils.LogInfo("成功创建锁文件，允许启动")
	return true
}

// isProcessRunning checks if a process with the given PID is running.
func isProcessRunning(pid int) bool {
	utils.LogInfo("检查进程是否运行", zap.Int("pid", pid))

	process, err := os.FindProcess(pid)
	if err != nil {
		utils.LogWarning("查找进程失败", zap.Int("pid", pid), zap.Error(err))
		return false
	}

	err = process.Signal(syscall.Signal(0))
	if err == nil {
		utils.LogInfo("进程存在（通过Signal检查）", zap.Int("pid", pid))
		return true
	}
	utils.LogInfo("Signal检查失败，尝试tasklist", zap.Error(err))

	cmdStr := fmt.Sprintf("tasklist /FI \"PID eq %d\" /NH", pid)
	utils.LogInfo("执行命令", zap.String("command", cmdStr))

	cmd := exec.Command("cmd", "/c", cmdStr)
	out, err := cmd.Output()
	if err != nil {
		utils.LogWarning("tasklist命令失败", zap.Error(err))
		return false
	}

	output := strings.TrimSpace(string(out))
	utils.LogInfo("tasklist输出", zap.String("output", output))

	if len(output) == 0 {
		utils.LogInfo("进程不存在（空输出）", zap.Int("pid", pid))
		return false
	}

	if strings.Contains(output, "INFO: No tasks are running") ||
		strings.Contains(output, "信息: 没有运行的任务匹配指定标准") {
		utils.LogInfo("进程不存在（tasklist未返回匹配）", zap.Int("pid", pid))
		return false
	}

	utils.LogInfo("进程存在（通过tasklist确认）", zap.Int("pid", pid))
	return true
}

// cleanupLockFile removes the lock file.
func cleanupLockFile() {
	utils.LogInfo("清理锁文件...")

	if lockFile != nil {
		utils.LogInfo("关闭锁文件句柄")
		lockFile.Close()
		lockFile = nil
	}

	if lockFilePath != "" {
		if err := os.Remove(lockFilePath); err != nil {
			utils.LogWarning("删除锁文件失败", zap.Error(err))
		} else {
			utils.LogInfo("成功删除锁文件", zap.String("path", lockFilePath))
		}
		lockFilePath = ""
	} else {
		utils.LogInfo("没有锁文件需要清理")
	}
}

func main() {
	// 初始化zap日志系统
	logger, err := utils.NewLogger(zapcore.InfoLevel)
	if err != nil {
		log.Fatalf("初始化日志系统失败: %v", err)
	}
	defer logger.Sync()

	// 设置全局日志记录器
	utils.Logger = logger

	// 记录应用启动
	utils.LogInfo("应用程序启动", zap.String("version", "1.0.0"))

	utils.LogInfo("启动磁感分析操作台")

	// Check for single instance
	utils.LogInfo("开始单实例检查")
	if !checkSingleInstance() {
		utils.LogWarning("单实例检查失败，程序退出")
		fmt.Println("磁感分析操作台已在运行中，请勿重复启动！")
		os.Exit(1)
	}
	utils.LogInfo("单实例检查通过")

	// 设置程序退出时清理锁文件
	defer cleanupLockFile()

	// 设置信号处理，确保程序被强制终止时也能清理锁文件
	c := make(chan os.Signal, 1)
	signal.Notify(c, os.Interrupt, syscall.SIGTERM)
	go func() {
		<-c
		utils.LogInfo("收到退出信号，开始清理...")
		cleanupLockFile()
		utils.LogInfo("清理完成，程序退出")
		os.Exit(0)
	}()

	// Create an instance of the app structure
	app := NewApp()

	// Create application with options
	err = wails.Run(&options.App{
		Title:             "磁感分析操作台",
		Width:             424, // 根据配置文件计算的宽度
		Height:            724, // 根据配置文件计算的高度
		MinWidth:          300,
		MinHeight:         600,
		MaxWidth:          1400,
		MaxHeight:         1000,
		StartHidden:       true, // 启动时隐藏，等位置设置完成后显示
		HideWindowOnClose: false,
		AlwaysOnTop:       false, // 始终置顶
		AssetServer: &assetserver.Options{
			Assets: assets,
		},
		BackgroundColour: &options.RGBA{R: 240, G: 242, B: 245, A: 1},
		OnStartup:        app.startup,
		OnDomReady:       app.domReady,
		OnBeforeClose:    app.shutdown,
		Bind: []interface{}{
			app,
		},
	})

	if err != nil {
		println("Error:", err.Error())
	}
}
