package main

// 这个文件展示了需要对app.go进行的修改
// 用于集成统一轮次管理器

import (
	"context"
	"fmt"
	"time"
	
	"MagneticOperator/app/models"
	"MagneticOperator/app/services"
	"MagneticOperator/app/utils"
	"go.uber.org/zap"
)

// 需要在App结构体中添加的字段：
// 在现有字段后添加：
//
// // 统一轮次管理器 - 新的统一轮次管理系统
// unifiedRoundIntegration *services.UnifiedRoundIntegration
//
// 并将现有字段标记为已弃用：
// // 轮次管理 - 用于跟踪每个用户的轮次状态 (已弃用，保留用于兼容性)
// roundManager map[string]*RoundStatus
//
// // 10轮检测数据收集 (已弃用，保留用于兼容性)
// currentUserCheckingInfo map[string]*models.CurrentUserCheckingInfo
// checkingInfoMutex       sync.RWMutex // 保护用户检测信息的并发访问 (已弃用，保留用于兼容性)

// 需要修改NewApp函数，添加统一轮次管理器的初始化：
func NewAppWithUnifiedRoundManager() *App {
	app := &App{
		currentUserCheckingInfo: make(map[string]*models.CurrentUserCheckingInfo),
		ocrTaskContexts:         make(map[string]*OCRTaskContext),
		roundManager:           make(map[string]*RoundStatus), // 保留用于兼容性
	}
	
	// 初始化统一轮次管理器
	app.unifiedRoundIntegration = services.NewUnifiedRoundIntegration(app)
	
	return app
}

// 需要在startup函数中启动统一轮次管理器：
func (a *App) startupWithUnifiedRoundManager(ctx context.Context) {
	// 现有的startup逻辑...
	
	// 启动统一轮次管理器
	if a.unifiedRoundIntegration != nil {
		if err := a.unifiedRoundIntegration.Start(); err != nil {
			utils.LogError("启动统一轮次管理器失败", zap.Error(err))
		} else {
			utils.LogInfo("统一轮次管理器启动成功")
			
			// 从旧系统迁移数据
			if err := a.unifiedRoundIntegration.MigrateFromLegacyRoundManager(); err != nil {
				utils.LogWarn("迁移轮次数据失败", zap.Error(err))
			} else {
				utils.LogInfo("轮次数据迁移成功")
			}
		}
	}
}

// 需要添加的兼容性方法，用于替换现有的轮次管理方法：

// markModeCompleted 兼容性方法 - 使用统一轮次管理器
func (a *App) markModeCompletedUnified(userName, mode string) (int, bool, int) {
	if a.unifiedRoundIntegration != nil {
		return a.unifiedRoundIntegration.GetLegacyCompatibleMethods().MarkModeCompleted(userName, mode)
	}
	// 回退到旧方法
	return a.markModeCompleted(userName, mode)
}

// getCurrentRound 兼容性方法 - 使用统一轮次管理器
func (a *App) getCurrentRoundUnified(userName string) int {
	if a.unifiedRoundIntegration != nil {
		return a.unifiedRoundIntegration.GetLegacyCompatibleMethods().GetCurrentRound(userName)
	}
	// 回退到旧方法
	return a.getCurrentRound(userName)
}

// GetCurrentRoundNumber 兼容性方法 - 使用统一轮次管理器
func (a *App) GetCurrentRoundNumberUnified(userName string) int {
	if a.unifiedRoundIntegration != nil {
		return a.unifiedRoundIntegration.GetLegacyCompatibleMethods().GetCurrentRoundNumber(userName)
	}
	// 回退到旧方法
	return a.GetCurrentRoundNumber(userName)
}

// updateCurrentUserCheckingInfo 兼容性方法 - 使用统一轮次管理器
func (a *App) updateCurrentUserCheckingInfoUnified(userName, mode, imagePath, organName string, ocrResult *services.OCRResult, dHealthTrend map[string]string, currentUser *models.Registration) error {
	if a.unifiedRoundIntegration != nil {
		// 使用统一管理器的数据更新方法
		userKey := a.generateUserKey(userName)
		return a.unifiedRoundIntegration.GetLegacyCompatibleMethods().UpdateCurrentUserCheckingInfo(userKey, func(info *models.CurrentUserCheckingInfo) {
			// 这里需要实现具体的更新逻辑
			// 将原有的updateCurrentUserCheckingInfo逻辑迁移到这里
		})
	}
	// 回退到旧方法
	return a.updateCurrentUserCheckingInfo(userName, mode, imagePath, organName, ocrResult, dHealthTrend, currentUser)
}

// generateUserKey 生成用户键
func (a *App) generateUserKey(userName string) string {
	// 使用与统一轮次管理器相同的键生成逻辑
	return fmt.Sprintf("%s_%s", userName, time.Now().Format("2006-01-02"))
}

// 需要添加的管理方法：

// GetUnifiedRoundManager 获取统一轮次管理器
func (a *App) GetUnifiedRoundManager() *services.UnifiedRoundManager {
	if a.unifiedRoundIntegration != nil {
		return a.unifiedRoundIntegration.GetManager()
	}
	return nil
}

// GetUnifiedRoundStatus 获取统一轮次状态
func (a *App) GetUnifiedRoundStatus(userName string) *services.UnifiedRoundStatus {
	if manager := a.GetUnifiedRoundManager(); manager != nil {
		return manager.GetRoundStatus(userName)
	}
	return nil
}

// IsUnifiedRoundManagerEnabled 检查统一轮次管理器是否启用
func (a *App) IsUnifiedRoundManagerEnabled() bool {
	return a.unifiedRoundIntegration != nil && a.unifiedRoundIntegration.GetManager().IsRunning()
}