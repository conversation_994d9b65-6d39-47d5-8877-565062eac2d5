export namespace models {
	
	export class CloudFunctionConfig {
	    registrations_url: string;
	    screenshot_records_url: string;
	    siteInfoByDeviceMAC_url: string;
	    mark_patient_completed_url: string;
	
	    static createFrom(source: any = {}) {
	        return new CloudFunctionConfig(source);
	    }
	
	    constructor(source: any = {}) {
	        if ('string' === typeof source) source = JSON.parse(source);
	        this.registrations_url = source["registrations_url"];
	        this.screenshot_records_url = source["screenshot_records_url"];
	        this.siteInfoByDeviceMAC_url = source["siteInfoByDeviceMAC_url"];
	        this.mark_patient_completed_url = source["mark_patient_completed_url"];
	    }
	}
	export class CozeConfig {
	    token: string;
	    workflow_id_post_pic: string;
	    workflow_id_post_registration: string;
	    workflow_id_user_info: string;
	    space_id: string;
	    app_id: string;
	
	    static createFrom(source: any = {}) {
	        return new CozeConfig(source);
	    }
	
	    constructor(source: any = {}) {
	        if ('string' === typeof source) source = JSON.parse(source);
	        this.token = source["token"];
	        this.workflow_id_post_pic = source["workflow_id_post_pic"];
	        this.workflow_id_post_registration = source["workflow_id_post_registration"];
	        this.workflow_id_user_info = source["workflow_id_user_info"];
	        this.space_id = source["space_id"];
	        this.app_id = source["app_id"];
	    }
	}
	export class OCRConfig {
	    api_url: string;
	    table_api_url: string;
	    token: string;
	
	    static createFrom(source: any = {}) {
	        return new OCRConfig(source);
	    }
	
	    constructor(source: any = {}) {
	        if ('string' === typeof source) source = JSON.parse(source);
	        this.api_url = source["api_url"];
	        this.table_api_url = source["table_api_url"];
	        this.token = source["token"];
	    }
	}
	export class APIKeys {
	    ocr: OCRConfig;
	    coze: CozeConfig;
	    cloud_function: CloudFunctionConfig;
	
	    static createFrom(source: any = {}) {
	        return new APIKeys(source);
	    }
	
	    constructor(source: any = {}) {
	        if ('string' === typeof source) source = JSON.parse(source);
	        this.ocr = this.convertValues(source["ocr"], OCRConfig);
	        this.coze = this.convertValues(source["coze"], CozeConfig);
	        this.cloud_function = this.convertValues(source["cloud_function"], CloudFunctionConfig);
	    }
	
		convertValues(a: any, classs: any, asMap: boolean = false): any {
		    if (!a) {
		        return a;
		    }
		    if (a.slice && a.map) {
		        return (a as any[]).map(elem => this.convertValues(elem, classs));
		    } else if ("object" === typeof a) {
		        if (asMap) {
		            for (const key of Object.keys(a)) {
		                a[key] = new classs(a[key]);
		            }
		            return a;
		        }
		        return new classs(a);
		    }
		    return a;
		}
	}
	export class ColorDetectionConfig {
	    debug_mode: boolean;
	    save_debug_files: boolean;
	
	    static createFrom(source: any = {}) {
	        return new ColorDetectionConfig(source);
	    }
	
	    constructor(source: any = {}) {
	        if ('string' === typeof source) source = JSON.parse(source);
	        this.debug_mode = source["debug_mode"];
	        this.save_debug_files = source["save_debug_files"];
	    }
	}
	export class ConfigUserInfo {
	    name: string;
	    birth: string;
	    id_number: string;
	
	    static createFrom(source: any = {}) {
	        return new ConfigUserInfo(source);
	    }
	
	    constructor(source: any = {}) {
	        if ('string' === typeof source) source = JSON.parse(source);
	        this.name = source["name"];
	        this.birth = source["birth"];
	        this.id_number = source["id_number"];
	    }
	}
	export class DeviceInfo {
	    mac_address: string;
	    device_name: string;
	
	    static createFrom(source: any = {}) {
	        return new DeviceInfo(source);
	    }
	
	    constructor(source: any = {}) {
	        if ('string' === typeof source) source = JSON.parse(source);
	        this.mac_address = source["mac_address"];
	        this.device_name = source["device_name"];
	    }
	}
	export class CropSettings {
	    top_percent: number;
	    bottom_percent: number;
	    left_percent: number;
	    right_percent: number;
	    always_on_top: boolean;
	
	    static createFrom(source: any = {}) {
	        return new CropSettings(source);
	    }
	
	    constructor(source: any = {}) {
	        if ('string' === typeof source) source = JSON.parse(source);
	        this.top_percent = source["top_percent"];
	        this.bottom_percent = source["bottom_percent"];
	        this.left_percent = source["left_percent"];
	        this.right_percent = source["right_percent"];
	        this.always_on_top = source["always_on_top"];
	    }
	}
	export class Contact {
	    manager: string;
	    phone: string;
	
	    static createFrom(source: any = {}) {
	        return new Contact(source);
	    }
	
	    constructor(source: any = {}) {
	        if ('string' === typeof source) source = JSON.parse(source);
	        this.manager = source["manager"];
	        this.phone = source["phone"];
	    }
	}
	export class Location {
	    province: string;
	    city: string;
	    district: string;
	    address: string;
	
	    static createFrom(source: any = {}) {
	        return new Location(source);
	    }
	
	    constructor(source: any = {}) {
	        if ('string' === typeof source) source = JSON.parse(source);
	        this.province = source["province"];
	        this.city = source["city"];
	        this.district = source["district"];
	        this.address = source["address"];
	    }
	}
	export class SiteInfo {
	    site_id: string;
	    site_name: string;
	    site_type: string;
	    parent_org: string;
	    location: Location;
	    contact: Contact;
	
	    static createFrom(source: any = {}) {
	        return new SiteInfo(source);
	    }
	
	    constructor(source: any = {}) {
	        if ('string' === typeof source) source = JSON.parse(source);
	        this.site_id = source["site_id"];
	        this.site_name = source["site_name"];
	        this.site_type = source["site_type"];
	        this.parent_org = source["parent_org"];
	        this.location = this.convertValues(source["location"], Location);
	        this.contact = this.convertValues(source["contact"], Contact);
	    }
	
		convertValues(a: any, classs: any, asMap: boolean = false): any {
		    if (!a) {
		        return a;
		    }
		    if (a.slice && a.map) {
		        return (a as any[]).map(elem => this.convertValues(elem, classs));
		    } else if ("object" === typeof a) {
		        if (asMap) {
		            for (const key of Object.keys(a)) {
		                a[key] = new classs(a[key]);
		            }
		            return a;
		        }
		        return new classs(a);
		    }
		    return a;
		}
	}
	export class MpAppInfo {
	    appid: string;
	    target_page: string;
	
	    static createFrom(source: any = {}) {
	        return new MpAppInfo(source);
	    }
	
	    constructor(source: any = {}) {
	        if ('string' === typeof source) source = JSON.parse(source);
	        this.appid = source["appid"];
	        this.target_page = source["target_page"];
	    }
	}
	export class AppConfig {
	    mp_app_info: MpAppInfo;
	    site_info: SiteInfo;
	    crop_settings: CropSettings;
	    normal_windows_setting: CropSettings;
	    expanded_crop_settings: CropSettings;
	    api_keys: APIKeys;
	    device_info: DeviceInfo;
	    user_info: ConfigUserInfo;
	    use_system_notification: boolean;
	    color_detection: ColorDetectionConfig;
	    environment?: string;
	    debug?: boolean;
	
	    static createFrom(source: any = {}) {
	        return new AppConfig(source);
	    }
	
	    constructor(source: any = {}) {
	        if ('string' === typeof source) source = JSON.parse(source);
	        this.mp_app_info = this.convertValues(source["mp_app_info"], MpAppInfo);
	        this.site_info = this.convertValues(source["site_info"], SiteInfo);
	        this.crop_settings = this.convertValues(source["crop_settings"], CropSettings);
	        this.normal_windows_setting = this.convertValues(source["normal_windows_setting"], CropSettings);
	        this.expanded_crop_settings = this.convertValues(source["expanded_crop_settings"], CropSettings);
	        this.api_keys = this.convertValues(source["api_keys"], APIKeys);
	        this.device_info = this.convertValues(source["device_info"], DeviceInfo);
	        this.user_info = this.convertValues(source["user_info"], ConfigUserInfo);
	        this.use_system_notification = source["use_system_notification"];
	        this.color_detection = this.convertValues(source["color_detection"], ColorDetectionConfig);
	        this.environment = source["environment"];
	        this.debug = source["debug"];
	    }
	
		convertValues(a: any, classs: any, asMap: boolean = false): any {
		    if (!a) {
		        return a;
		    }
		    if (a.slice && a.map) {
		        return (a as any[]).map(elem => this.convertValues(elem, classs));
		    } else if ("object" === typeof a) {
		        if (asMap) {
		            for (const key of Object.keys(a)) {
		                a[key] = new classs(a[key]);
		            }
		            return a;
		        }
		        return new classs(a);
		    }
		    return a;
		}
	}
	
	
	
	
	
	
	export class ScreenshotOCRData {
	    screenshotMode: string;
	    screenshotTime: string;
	    imagePath: string;
	    imageFileName: string;
	    detectedOrgan: string;
	    fullOCRText: string;
	    ocrConfidence: number;
	    dHealthTrend: Record<string, string>;
	    dValueCount: number;
	    rawOCRResponse: any;
	    processingStatus: string;
	    errorMessage?: string;
	
	    static createFrom(source: any = {}) {
	        return new ScreenshotOCRData(source);
	    }
	
	    constructor(source: any = {}) {
	        if ('string' === typeof source) source = JSON.parse(source);
	        this.screenshotMode = source["screenshotMode"];
	        this.screenshotTime = source["screenshotTime"];
	        this.imagePath = source["imagePath"];
	        this.imageFileName = source["imageFileName"];
	        this.detectedOrgan = source["detectedOrgan"];
	        this.fullOCRText = source["fullOCRText"];
	        this.ocrConfidence = source["ocrConfidence"];
	        this.dHealthTrend = source["dHealthTrend"];
	        this.dValueCount = source["dValueCount"];
	        this.rawOCRResponse = source["rawOCRResponse"];
	        this.processingStatus = source["processingStatus"];
	        this.errorMessage = source["errorMessage"];
	    }
	}
	export class RoundData {
	    roundNumber: number;
	    roundStartTime: string;
	    roundEndTime: string;
	    b02Data?: ScreenshotOCRData;
	    c03Data?: ScreenshotOCRData;
	    roundCompleted: boolean;
	
	    static createFrom(source: any = {}) {
	        return new RoundData(source);
	    }
	
	    constructor(source: any = {}) {
	        if ('string' === typeof source) source = JSON.parse(source);
	        this.roundNumber = source["roundNumber"];
	        this.roundStartTime = source["roundStartTime"];
	        this.roundEndTime = source["roundEndTime"];
	        this.b02Data = this.convertValues(source["b02Data"], ScreenshotOCRData);
	        this.c03Data = this.convertValues(source["c03Data"], ScreenshotOCRData);
	        this.roundCompleted = source["roundCompleted"];
	    }
	
		convertValues(a: any, classs: any, asMap: boolean = false): any {
		    if (!a) {
		        return a;
		    }
		    if (a.slice && a.map) {
		        return (a as any[]).map(elem => this.convertValues(elem, classs));
		    } else if ("object" === typeof a) {
		        if (asMap) {
		            for (const key of Object.keys(a)) {
		                a[key] = new classs(a[key]);
		            }
		            return a;
		        }
		        return new classs(a);
		    }
		    return a;
		}
	}
	export class CurrentUserCheckingInfo {
	    userID: string;
	    userName: string;
	    siteName: string;
	    registrationNo: string;
	    checkingTime: string;
	    completionTime: string;
	    totalRounds: number;
	    completedRounds: number;
	    roundsData: RoundData[];
	    totalScreenshots: number;
	    processedOCRs: number;
	    detectedOrgans: Record<string, number>;
	
	    static createFrom(source: any = {}) {
	        return new CurrentUserCheckingInfo(source);
	    }
	
	    constructor(source: any = {}) {
	        if ('string' === typeof source) source = JSON.parse(source);
	        this.userID = source["userID"];
	        this.userName = source["userName"];
	        this.siteName = source["siteName"];
	        this.registrationNo = source["registrationNo"];
	        this.checkingTime = source["checkingTime"];
	        this.completionTime = source["completionTime"];
	        this.totalRounds = source["totalRounds"];
	        this.completedRounds = source["completedRounds"];
	        this.roundsData = this.convertValues(source["roundsData"], RoundData);
	        this.totalScreenshots = source["totalScreenshots"];
	        this.processedOCRs = source["processedOCRs"];
	        this.detectedOrgans = source["detectedOrgans"];
	    }
	
		convertValues(a: any, classs: any, asMap: boolean = false): any {
		    if (!a) {
		        return a;
		    }
		    if (a.slice && a.map) {
		        return (a as any[]).map(elem => this.convertValues(elem, classs));
		    } else if ("object" === typeof a) {
		        if (asMap) {
		            for (const key of Object.keys(a)) {
		                a[key] = new classs(a[key]);
		            }
		            return a;
		        }
		        return new classs(a);
		    }
		    return a;
		}
	}
	
	
	
	
	export class Patient {
	    name: string;
	    registration_code: string;
	    full_code: string;
	    register_time: string;
	
	    static createFrom(source: any = {}) {
	        return new Patient(source);
	    }
	
	    constructor(source: any = {}) {
	        if ('string' === typeof source) source = JSON.parse(source);
	        this.name = source["name"];
	        this.registration_code = source["registration_code"];
	        this.full_code = source["full_code"];
	        this.register_time = source["register_time"];
	    }
	}
	export class UserInfo {
	    name: string;
	    birthday: string;
	    age: number;
	    gender: number;
	    id_number: string;
	
	    static createFrom(source: any = {}) {
	        return new UserInfo(source);
	    }
	
	    constructor(source: any = {}) {
	        if ('string' === typeof source) source = JSON.parse(source);
	        this.name = source["name"];
	        this.birthday = source["birthday"];
	        this.age = source["age"];
	        this.gender = source["gender"];
	        this.id_number = source["id_number"];
	    }
	}
	export class Registration {
	    _id: string;
	    user_id: string;
	    device_no: string;
	    site_id: string;
	    registration_number: string;
	    registration_time: number;
	    registration_date: string;
	    userInfo: UserInfo[];
	    user_confirmed: boolean;
	    health_check_completed: boolean;
	    health_check_result_analyzed: boolean;
	    is_completed?: boolean;
	    completion_time?: string;
	    name?: string;
	    number?: string;
	    register_time?: string;
	
	    static createFrom(source: any = {}) {
	        return new Registration(source);
	    }
	
	    constructor(source: any = {}) {
	        if ('string' === typeof source) source = JSON.parse(source);
	        this._id = source["_id"];
	        this.user_id = source["user_id"];
	        this.device_no = source["device_no"];
	        this.site_id = source["site_id"];
	        this.registration_number = source["registration_number"];
	        this.registration_time = source["registration_time"];
	        this.registration_date = source["registration_date"];
	        this.userInfo = this.convertValues(source["userInfo"], UserInfo);
	        this.user_confirmed = source["user_confirmed"];
	        this.health_check_completed = source["health_check_completed"];
	        this.health_check_result_analyzed = source["health_check_result_analyzed"];
	        this.is_completed = source["is_completed"];
	        this.completion_time = source["completion_time"];
	        this.name = source["name"];
	        this.number = source["number"];
	        this.register_time = source["register_time"];
	    }
	
		convertValues(a: any, classs: any, asMap: boolean = false): any {
		    if (!a) {
		        return a;
		    }
		    if (a.slice && a.map) {
		        return (a as any[]).map(elem => this.convertValues(elem, classs));
		    } else if ("object" === typeof a) {
		        if (asMap) {
		            for (const key of Object.keys(a)) {
		                a[key] = new classs(a[key]);
		            }
		            return a;
		        }
		        return new classs(a);
		    }
		    return a;
		}
	}
	
	
	

}

export namespace services {
	
	export class OCRResult {
	    organ_name: string;
	    confidence: number;
	    image_path: string;
	    key_value_pairs?: Record<string, string>;
	    raw_response?: number[];
	
	    static createFrom(source: any = {}) {
	        return new OCRResult(source);
	    }
	
	    constructor(source: any = {}) {
	        if ('string' === typeof source) source = JSON.parse(source);
	        this.organ_name = source["organ_name"];
	        this.confidence = source["confidence"];
	        this.image_path = source["image_path"];
	        this.key_value_pairs = source["key_value_pairs"];
	        this.raw_response = source["raw_response"];
	    }
	}
	export class TaskResult {
	    task_id: string;
	    status: string;
	    result: any;
	    error?: string;
	    duration: number;
	    // Go type: time
	    completed_at: any;
	
	    static createFrom(source: any = {}) {
	        return new TaskResult(source);
	    }
	
	    constructor(source: any = {}) {
	        if ('string' === typeof source) source = JSON.parse(source);
	        this.task_id = source["task_id"];
	        this.status = source["status"];
	        this.result = source["result"];
	        this.error = source["error"];
	        this.duration = source["duration"];
	        this.completed_at = this.convertValues(source["completed_at"], null);
	    }
	
		convertValues(a: any, classs: any, asMap: boolean = false): any {
		    if (!a) {
		        return a;
		    }
		    if (a.slice && a.map) {
		        return (a as any[]).map(elem => this.convertValues(elem, classs));
		    } else if ("object" === typeof a) {
		        if (asMap) {
		            for (const key of Object.keys(a)) {
		                a[key] = new classs(a[key]);
		            }
		            return a;
		        }
		        return new classs(a);
		    }
		    return a;
		}
	}

}

