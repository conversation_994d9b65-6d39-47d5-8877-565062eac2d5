{"level":"INFO","timestamp":"2025-07-01T21:50:18.362+0800","caller":"utils/logger.go:94","msg":"应用程序启动","version":"1.0.0"}
{"level":"INFO","timestamp":"2025-07-01T21:50:18.362+0800","caller":"utils/logger.go:94","msg":"启动磁感分析操作台"}
{"level":"INFO","timestamp":"2025-07-01T21:50:18.362+0800","caller":"utils/logger.go:94","msg":"开始单实例检查"}
{"level":"INFO","timestamp":"2025-07-01T21:50:18.362+0800","caller":"utils/logger.go:94","msg":"检查现有实例..."}
{"level":"INFO","timestamp":"2025-07-01T21:50:18.362+0800","caller":"utils/logger.go:94","msg":"锁文件路径","path":"C:\\Users\\<USER>\\AppData\\Local\\Temp\\MagneticOperator.lock"}
{"level":"INFO","timestamp":"2025-07-01T21:50:18.362+0800","caller":"utils/logger.go:94","msg":"未找到现有锁文件"}
{"level":"INFO","timestamp":"2025-07-01T21:50:18.362+0800","caller":"utils/logger.go:94","msg":"尝试创建新锁文件..."}
{"level":"INFO","timestamp":"2025-07-01T21:50:18.363+0800","caller":"utils/logger.go:94","msg":"写入当前PID到锁文件","pid":52464}
{"level":"INFO","timestamp":"2025-07-01T21:50:18.384+0800","caller":"utils/logger.go:94","msg":"成功创建锁文件，允许启动"}
{"level":"INFO","timestamp":"2025-07-01T21:50:18.384+0800","caller":"utils/logger.go:94","msg":"单实例检查通过"}
{"level":"INFO","timestamp":"2025-07-01T21:50:18.384+0800","caller":"utils/logger.go:94","msg":"创建App实例..."}
{"level":"INFO","timestamp":"2025-07-01T21:50:18.384+0800","caller":"utils/logger.go:94","msg":"App实例创建成功"}
{"level":"INFO","timestamp":"2025-07-01T21:50:18.384+0800","caller":"utils/logger.go:94","msg":"开始启动Wails应用..."}
{"level":"INFO","timestamp":"2025-07-01T21:50:18.403+0800","caller":"utils/logger.go:94","msg":"Wails.Run()调用完成"}
{"level":"INFO","timestamp":"2025-07-01T21:50:18.403+0800","caller":"utils/logger.go:94","msg":"Wails应用正常退出"}
{"level":"INFO","timestamp":"2025-07-01T21:50:18.403+0800","caller":"utils/logger.go:94","msg":"清理锁文件..."}
{"level":"INFO","timestamp":"2025-07-01T21:50:18.403+0800","caller":"utils/logger.go:94","msg":"关闭锁文件句柄"}
{"level":"INFO","timestamp":"2025-07-01T21:50:18.403+0800","caller":"utils/logger.go:94","msg":"成功删除锁文件","path":"C:\\Users\\<USER>\\AppData\\Local\\Temp\\MagneticOperator.lock"}
{"level":"INFO","timestamp":"2025-07-01T22:56:34.210+0800","caller":"utils/logger.go:94","msg":"应用程序启动","version":"1.0.0"}
{"level":"INFO","timestamp":"2025-07-01T22:56:34.210+0800","caller":"utils/logger.go:94","msg":"启动磁感分析操作台"}
{"level":"INFO","timestamp":"2025-07-01T22:56:34.210+0800","caller":"utils/logger.go:94","msg":"开始单实例检查"}
{"level":"INFO","timestamp":"2025-07-01T22:56:34.210+0800","caller":"utils/logger.go:94","msg":"检查现有实例..."}
{"level":"INFO","timestamp":"2025-07-01T22:56:34.210+0800","caller":"utils/logger.go:94","msg":"锁文件路径","path":"C:\\Users\\<USER>\\AppData\\Local\\Temp\\MagneticOperator.lock"}
{"level":"INFO","timestamp":"2025-07-01T22:56:34.210+0800","caller":"utils/logger.go:94","msg":"未找到现有锁文件"}
{"level":"INFO","timestamp":"2025-07-01T22:56:34.210+0800","caller":"utils/logger.go:94","msg":"尝试创建新锁文件..."}
{"level":"INFO","timestamp":"2025-07-01T22:56:34.211+0800","caller":"utils/logger.go:94","msg":"写入当前PID到锁文件","pid":47848}
{"level":"INFO","timestamp":"2025-07-01T22:56:34.213+0800","caller":"utils/logger.go:94","msg":"成功创建锁文件，允许启动"}
{"level":"INFO","timestamp":"2025-07-01T22:56:34.213+0800","caller":"utils/logger.go:94","msg":"单实例检查通过"}
{"level":"INFO","timestamp":"2025-07-01T22:56:34.213+0800","caller":"utils/logger.go:94","msg":"创建App实例..."}
{"level":"INFO","timestamp":"2025-07-01T22:56:34.213+0800","caller":"utils/logger.go:94","msg":"App实例创建成功"}
{"level":"INFO","timestamp":"2025-07-01T22:56:34.213+0800","caller":"utils/logger.go:94","msg":"开始启动Wails应用..."}
{"level":"INFO","timestamp":"2025-07-01T22:56:34.221+0800","caller":"utils/logger.go:94","msg":"Wails.Run()调用完成"}
{"level":"INFO","timestamp":"2025-07-01T22:56:34.221+0800","caller":"utils/logger.go:94","msg":"Wails应用正常退出"}
{"level":"INFO","timestamp":"2025-07-01T22:56:34.221+0800","caller":"utils/logger.go:94","msg":"清理锁文件..."}
{"level":"INFO","timestamp":"2025-07-01T22:56:34.221+0800","caller":"utils/logger.go:94","msg":"关闭锁文件句柄"}
{"level":"INFO","timestamp":"2025-07-01T22:56:34.222+0800","caller":"utils/logger.go:94","msg":"成功删除锁文件","path":"C:\\Users\\<USER>\\AppData\\Local\\Temp\\MagneticOperator.lock"}
{"level":"INFO","timestamp":"2025-07-01T22:56:41.482+0800","caller":"utils/logger.go:94","msg":"应用程序启动","version":"1.0.0"}
{"level":"INFO","timestamp":"2025-07-01T22:56:41.482+0800","caller":"utils/logger.go:94","msg":"启动磁感分析操作台"}
{"level":"INFO","timestamp":"2025-07-01T22:56:41.482+0800","caller":"utils/logger.go:94","msg":"开始单实例检查"}
{"level":"INFO","timestamp":"2025-07-01T22:56:41.482+0800","caller":"utils/logger.go:94","msg":"检查现有实例..."}
{"level":"INFO","timestamp":"2025-07-01T22:56:41.482+0800","caller":"utils/logger.go:94","msg":"锁文件路径","path":"C:\\Users\\<USER>\\AppData\\Local\\Temp\\MagneticOperator.lock"}
{"level":"INFO","timestamp":"2025-07-01T22:56:41.482+0800","caller":"utils/logger.go:94","msg":"未找到现有锁文件"}
{"level":"INFO","timestamp":"2025-07-01T22:56:41.482+0800","caller":"utils/logger.go:94","msg":"尝试创建新锁文件..."}
{"level":"INFO","timestamp":"2025-07-01T22:56:41.483+0800","caller":"utils/logger.go:94","msg":"写入当前PID到锁文件","pid":41824}
{"level":"INFO","timestamp":"2025-07-01T22:56:41.503+0800","caller":"utils/logger.go:94","msg":"成功创建锁文件，允许启动"}
{"level":"INFO","timestamp":"2025-07-01T22:56:41.504+0800","caller":"utils/logger.go:94","msg":"单实例检查通过"}
{"level":"INFO","timestamp":"2025-07-01T22:56:41.504+0800","caller":"utils/logger.go:94","msg":"创建App实例..."}
{"level":"INFO","timestamp":"2025-07-01T22:56:41.504+0800","caller":"utils/logger.go:94","msg":"App实例创建成功"}
{"level":"INFO","timestamp":"2025-07-01T22:56:41.504+0800","caller":"utils/logger.go:94","msg":"开始启动Wails应用..."}
{"level":"INFO","timestamp":"2025-07-01T22:56:41.512+0800","caller":"utils/logger.go:94","msg":"Wails.Run()调用完成"}
{"level":"INFO","timestamp":"2025-07-01T22:56:41.512+0800","caller":"utils/logger.go:94","msg":"Wails应用正常退出"}
{"level":"INFO","timestamp":"2025-07-01T22:56:41.512+0800","caller":"utils/logger.go:94","msg":"清理锁文件..."}
{"level":"INFO","timestamp":"2025-07-01T22:56:41.512+0800","caller":"utils/logger.go:94","msg":"关闭锁文件句柄"}
{"level":"INFO","timestamp":"2025-07-01T22:56:41.512+0800","caller":"utils/logger.go:94","msg":"成功删除锁文件","path":"C:\\Users\\<USER>\\AppData\\Local\\Temp\\MagneticOperator.lock"}
{"level":"INFO","timestamp":"2025-07-01T22:56:45.421+0800","caller":"utils/logger.go:94","msg":"应用程序启动","version":"1.0.0"}
{"level":"INFO","timestamp":"2025-07-01T22:56:45.421+0800","caller":"utils/logger.go:94","msg":"启动磁感分析操作台"}
{"level":"INFO","timestamp":"2025-07-01T22:56:45.421+0800","caller":"utils/logger.go:94","msg":"开始单实例检查"}
{"level":"INFO","timestamp":"2025-07-01T22:56:45.421+0800","caller":"utils/logger.go:94","msg":"检查现有实例..."}
{"level":"INFO","timestamp":"2025-07-01T22:56:45.421+0800","caller":"utils/logger.go:94","msg":"锁文件路径","path":"F:\\myHbuilderAPP\\MagneticOperator\\build\\bin\\MagneticOperator.lock"}
{"level":"INFO","timestamp":"2025-07-01T22:56:45.421+0800","caller":"utils/logger.go:94","msg":"未找到现有锁文件"}
{"level":"INFO","timestamp":"2025-07-01T22:56:45.422+0800","caller":"utils/logger.go:94","msg":"尝试创建新锁文件..."}
{"level":"INFO","timestamp":"2025-07-01T22:56:45.422+0800","caller":"utils/logger.go:94","msg":"写入当前PID到锁文件","pid":23160}
{"level":"INFO","timestamp":"2025-07-01T22:56:45.510+0800","caller":"utils/logger.go:94","msg":"成功创建锁文件，允许启动"}
{"level":"INFO","timestamp":"2025-07-01T22:56:45.511+0800","caller":"utils/logger.go:94","msg":"单实例检查通过"}
{"level":"INFO","timestamp":"2025-07-01T22:56:45.511+0800","caller":"utils/logger.go:94","msg":"创建App实例..."}
{"level":"INFO","timestamp":"2025-07-01T22:56:45.511+0800","caller":"utils/logger.go:94","msg":"App实例创建成功"}
{"level":"INFO","timestamp":"2025-07-01T22:56:45.511+0800","caller":"utils/logger.go:94","msg":"开始启动Wails应用..."}
{"level":"INFO","timestamp":"2025-07-01T22:56:46.664+0800","caller":"utils/logger.go:94","msg":"=== STARTUP函数被调用 ==="}
{"level":"INFO","timestamp":"2025-07-01T22:56:46.665+0800","caller":"utils/logger.go:94","msg":"日志系统初始化成功"}
{"level":"INFO","timestamp":"2025-07-01T22:56:46.665+0800","caller":"utils/logger.go:94","msg":"消息配置系统初始化成功"}
{"level":"INFO","timestamp":"2025-07-01T22:56:46.666+0800","caller":"utils/logger.go:94","msg":"开始初始化服务..."}
{"level":"INFO","timestamp":"2025-07-01T22:56:46.666+0800","caller":"utils/logger.go:94","msg":"开始调用 initServices()..."}
{"level":"INFO","timestamp":"2025-07-01T22:56:46.666+0800","caller":"utils/logger.go:94","msg":"开始初始化服务..."}
{"level":"INFO","timestamp":"2025-07-01T22:56:46.671+0800","caller":"utils/logger.go:94","msg":"成功加载配置文件"}
{"level":"INFO","timestamp":"2025-07-01T22:56:46.674+0800","caller":"utils/logger.go:94","msg":"集成并发截图服务初始化成功"}
{"level":"INFO","timestamp":"2025-07-01T22:56:46.674+0800","caller":"utils/logger.go:94","msg":"开始初始化任务管理器..."}
{"level":"INFO","timestamp":"2025-07-01T22:56:46.674+0800","caller":"utils/logger.go:94","msg":"开始创建任务处理器..."}
{"level":"INFO","timestamp":"2025-07-01T22:56:46.674+0800","caller":"utils/logger.go:94","msg":"截图任务处理器创建完成"}
{"level":"INFO","timestamp":"2025-07-01T22:56:46.675+0800","caller":"utils/logger.go:94","msg":"OCR任务处理器创建完成"}
{"level":"INFO","timestamp":"2025-07-01T22:56:46.675+0800","caller":"utils/logger.go:94","msg":"上传任务处理器创建完成"}
{"level":"INFO","timestamp":"2025-07-01T22:56:46.675+0800","caller":"utils/logger.go:94","msg":"开始注册任务处理器..."}
{"level":"INFO","timestamp":"2025-07-01T22:56:46.675+0800","caller":"utils/logger.go:94","msg":"任务处理器注册成功","taskType":"screenshot_A"}
{"level":"INFO","timestamp":"2025-07-01T22:56:46.675+0800","caller":"utils/logger.go:94","msg":"截图A任务处理器注册成功"}
{"level":"INFO","timestamp":"2025-07-01T22:56:46.675+0800","caller":"utils/logger.go:94","msg":"任务处理器注册成功","taskType":"screenshot_B"}
{"level":"INFO","timestamp":"2025-07-01T22:56:46.675+0800","caller":"utils/logger.go:94","msg":"截图B任务处理器注册成功"}
{"level":"INFO","timestamp":"2025-07-01T22:56:46.675+0800","caller":"utils/logger.go:94","msg":"任务处理器注册成功","taskType":"screenshot_C"}
{"level":"INFO","timestamp":"2025-07-01T22:56:46.675+0800","caller":"utils/logger.go:94","msg":"截图C任务处理器注册成功"}
{"level":"INFO","timestamp":"2025-07-01T22:56:46.676+0800","caller":"utils/logger.go:94","msg":"任务处理器注册成功","taskType":"ocr_process"}
{"level":"INFO","timestamp":"2025-07-01T22:56:46.676+0800","caller":"utils/logger.go:94","msg":"OCR任务处理器注册成功"}
{"level":"INFO","timestamp":"2025-07-01T22:56:46.676+0800","caller":"utils/logger.go:94","msg":"任务处理器注册成功","taskType":"upload"}
{"level":"INFO","timestamp":"2025-07-01T22:56:46.676+0800","caller":"utils/logger.go:94","msg":"上传任务处理器注册成功"}
{"level":"INFO","timestamp":"2025-07-01T22:56:46.676+0800","caller":"utils/logger.go:94","msg":"开始启动任务管理器..."}
{"level":"INFO","timestamp":"2025-07-01T22:56:46.676+0800","caller":"utils/logger.go:94","msg":"任务管理器启动成功","maxConcurrent":3,"registeredHandlers":5,"cooldownPeriod":0.5}
{"level":"INFO","timestamp":"2025-07-01T22:56:46.676+0800","caller":"utils/logger.go:94","msg":"启动工作协程","workerCount":3}
{"level":"INFO","timestamp":"2025-07-01T22:56:46.676+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":0}
{"level":"INFO","timestamp":"2025-07-01T22:56:46.676+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":1}
{"level":"INFO","timestamp":"2025-07-01T22:56:46.677+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":2}
{"level":"INFO","timestamp":"2025-07-01T22:56:46.676+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":0}
{"level":"INFO","timestamp":"2025-07-01T22:56:46.677+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":1}
{"level":"INFO","timestamp":"2025-07-01T22:56:46.678+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":2}
{"level":"INFO","timestamp":"2025-07-01T22:56:46.678+0800","caller":"utils/logger.go:94","msg":"清理协程启动成功"}
{"level":"INFO","timestamp":"2025-07-01T22:56:46.679+0800","caller":"utils/logger.go:94","msg":"任务管理器启动事件已发送"}
{"level":"INFO","timestamp":"2025-07-01T22:56:46.679+0800","caller":"utils/logger.go:94","msg":"任务管理器启动成功"}
{"level":"INFO","timestamp":"2025-07-01T22:56:46.679+0800","caller":"utils/logger.go:94","msg":"任务管理器初始化成功"}
{"level":"INFO","timestamp":"2025-07-01T22:56:46.679+0800","caller":"utils/logger.go:94","msg":"开始从API加载站点信息..."}
{"level":"INFO","timestamp":"2025-07-01T22:56:47.279+0800","caller":"utils/logger.go:94","msg":"GetConfig called - SiteInfo: 北京市通州区潞城镇潞城社区卫生服务中心 (YL-BJ-TZ-001)"}
{"level":"INFO","timestamp":"2025-07-01T22:56:47.342+0800","caller":"utils/logger.go:94","msg":"DOM准备就绪，开始设置窗口位置和尺寸"}
{"level":"INFO","timestamp":"2025-07-01T22:56:47.351+0800","caller":"utils/logger.go:94","msg":"GetSiteInfo called - 开始获取站点信息"}
{"level":"INFO","timestamp":"2025-07-01T22:56:47.352+0800","caller":"utils/logger.go:94","msg":"使用缓存的站点信息"}
{"level":"INFO","timestamp":"2025-07-01T22:56:47.352+0800","caller":"utils/logger.go:94","msg":"GetSiteInfo - 成功获取站点信息: ID=YL-BJ-TZ-001, Name=北京市通州区潞城镇潞城社区卫生服务中心"}
{"level":"INFO","timestamp":"2025-07-01T22:56:47.365+0800","caller":"utils/logger.go:94","msg":"窗口位置设置为紧凑模式: x=2944, y=748, width=512, height=806"}
{"level":"INFO","timestamp":"2025-07-01T22:56:47.371+0800","caller":"utils/logger.go:94","msg":"窗体已设置为置顶"}
{"level":"INFO","timestamp":"2025-07-01T22:56:47.371+0800","caller":"utils/logger.go:94","msg":"窗体已显示"}
{"level":"INFO","timestamp":"2025-07-01T22:56:47.376+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T22:56:47.409+0800","caller":"utils/logger.go:94","msg":"站点信息无变化，复用现有二维码"}
{"level":"INFO","timestamp":"2025-07-01T22:56:47.409+0800","caller":"utils/logger.go:94","msg":"initServices() 完成"}
{"level":"INFO","timestamp":"2025-07-01T22:56:47.409+0800","caller":"utils/logger.go:94","msg":"开始创建并发截图服务..."}
{"level":"INFO","timestamp":"2025-07-01T22:56:47.409+0800","caller":"utils/logger.go:94","msg":"并发截图服务创建完成"}
{"level":"INFO","timestamp":"2025-07-01T22:56:47.409+0800","caller":"utils/logger.go:94","msg":"窗体状态初始化完成"}
{"level":"INFO","timestamp":"2025-07-01T22:56:47.409+0800","caller":"utils/logger.go:94","msg":"开始创建必要的目录..."}
{"level":"INFO","timestamp":"2025-07-01T22:56:47.410+0800","caller":"utils/logger.go:94","msg":"pic目录创建成功"}
{"level":"INFO","timestamp":"2025-07-01T22:56:47.410+0800","caller":"utils/logger.go:94","msg":"config目录创建成功"}
{"level":"INFO","timestamp":"2025-07-01T22:56:47.410+0800","caller":"utils/logger.go:94","msg":"开始启动快捷键监听..."}
{"level":"INFO","timestamp":"2025-07-01T22:56:47.410+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"启动快捷键监听","patient":"","site_id":""}
{"level":"INFO","timestamp":"2025-07-01T22:56:47.411+0800","caller":"utils/logger.go:94","msg":"快捷键服务启动成功"}
{"level":"INFO","timestamp":"2025-07-01T22:56:47.411+0800","caller":"utils/logger.go:94","msg":"启动OCR任务清理定时器..."}
{"level":"INFO","timestamp":"2025-07-01T22:56:47.411+0800","caller":"utils/logger.go:94","msg":"OCR任务清理定时器启动成功"}
{"level":"INFO","timestamp":"2025-07-01T22:56:47.412+0800","caller":"utils/logger.go:94","msg":"应用启动成功"}
{"level":"INFO","timestamp":"2025-07-01T22:56:47.987+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取待检测候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T22:56:47.987+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取指定日期已完成患者列表","patient":"2025-07-01","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T22:56:47.987+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取未分析患者列表","patient":"2025-07-01","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T22:56:48.641+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"生成报到二维码","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T22:56:48.641+0800","caller":"utils/logger.go:94","msg":"复用了缓存的报到二维码"}
{"level":"INFO","timestamp":"2025-07-01T22:56:48.648+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T22:56:48.893+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取待检测候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T22:56:49.065+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取指定日期已完成患者列表","patient":"2025-07-01","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T22:56:58.128+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"快捷键截图-模式B","patient":"","site_id":""}
{"level":"INFO","timestamp":"2025-07-01T22:56:58.128+0800","caller":"utils/logger.go:94","msg":"快捷键触发截图任务 - 模式: B"}
{"level":"INFO","timestamp":"2025-07-01T22:56:58.128+0800","caller":"utils/logger.go:94","msg":"收到截图任务请求: 快捷键B模式截图 (模式: B, 任务类型: screenshot_B)"}
{"level":"INFO","timestamp":"2025-07-01T22:56:58.128+0800","caller":"utils/logger.go:94","msg":"任务管理器可用，提交任务到队列"}
{"level":"INFO","timestamp":"2025-07-01T22:56:58.128+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T22:56:58.447+0800","caller":"utils/logger.go:94","msg":"收到任务提交请求","taskType":"screenshot_B","mode":"B","userName":"test-11200","roundNumber":0,"priority":"\u0002"}
{"level":"INFO","timestamp":"2025-07-01T22:56:58.447+0800","caller":"utils/logger.go:94","msg":"任务处理器检查通过","taskType":"screenshot_B"}
{"level":"INFO","timestamp":"2025-07-01T22:56:58.448+0800","caller":"utils/logger.go:94","msg":"任务创建成功","taskID":"screenshot_B_1751381818448177200_test-11200","taskType":"screenshot_B","mode":"B","userName":"test-11200","roundNumber":0,"timeout":35}
{"level":"INFO","timestamp":"2025-07-01T22:56:58.448+0800","caller":"utils/logger.go:94","msg":"任务提交成功","taskID":"screenshot_B_1751381818448177200_test-11200","taskType":"screenshot_B","userName":"test-11200","priority":2}
{"level":"INFO","timestamp":"2025-07-01T22:56:58.448+0800","caller":"utils/logger.go:94","msg":"开始处理任务","workerID":0,"taskID":"screenshot_B_1751381818448177200_test-11200","taskType":"screenshot_B","userName":"test-11200","mode":"B","roundNumber":0}
{"level":"INFO","timestamp":"2025-07-01T22:56:58.448+0800","caller":"utils/logger.go:94","msg":"成功提交快捷键B模式截图任务 - TaskID: screenshot_B_1751381818448177200_test-11200, User: test-11200, Round: 0"}
{"level":"INFO","timestamp":"2025-07-01T22:56:58.448+0800","caller":"utils/logger.go:94","msg":"开始处理任务","taskID":"screenshot_B_1751381818448177200_test-11200","taskType":"screenshot_B","workerID":0}
{"level":"INFO","timestamp":"2025-07-01T22:56:58.448+0800","caller":"utils/logger.go:94","msg":"截图任务已提交到任务管理器: 快捷键B模式截图"}
{"level":"INFO","timestamp":"2025-07-01T22:56:58.448+0800","caller":"utils/logger.go:94","msg":"执行任务处理器","taskID":"screenshot_B_1751381818448177200_test-11200","attempt":1}
{"level":"INFO","timestamp":"2025-07-01T22:56:58.448+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"执行截图任务-B","patient":"test-11200","site_id":""}
{"level":"INFO","timestamp":"2025-07-01T22:56:58.448+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T22:56:58.631+0800","caller":"utils/logger.go:94","msg":"[操作:快捷键截图-模式B] [当前受检者:test-11200] [网点:YL-BJ-TZ-001] [轮次:R01]"}
{"level":"INFO","timestamp":"2025-07-01T22:56:58.632+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取待检测候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T22:56:58.851+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"当前没有选中受检者，处于本系统操作者自行研究模式","patient":"暂无候检者","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T22:56:58.851+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"处理截图工作流程","patient":"test-11200","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T22:56:58.851+0800","caller":"utils/logger.go:94","msg":"开始处理截图工作流程 - 模式: B, 用户: test-11200\n"}
{"level":"INFO","timestamp":"2025-07-01T22:56:59.240+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T22:56:59.441+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"图片OCR识别","patient":"test-11200","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T22:57:02.372+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"快捷键截图-模式C","patient":"","site_id":""}
{"level":"INFO","timestamp":"2025-07-01T22:57:02.372+0800","caller":"utils/logger.go:94","msg":"快捷键触发截图任务 - 模式: C"}
{"level":"INFO","timestamp":"2025-07-01T22:57:02.372+0800","caller":"utils/logger.go:94","msg":"收到截图任务请求: 快捷键C模式截图 (模式: C, 任务类型: screenshot_C)"}
{"level":"INFO","timestamp":"2025-07-01T22:57:02.372+0800","caller":"utils/logger.go:94","msg":"任务管理器可用，提交任务到队列"}
{"level":"INFO","timestamp":"2025-07-01T22:57:02.372+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T22:57:02.547+0800","caller":"utils/logger.go:94","msg":"收到任务提交请求","taskType":"screenshot_C","mode":"C","userName":"test-11200","roundNumber":0,"priority":"\u0002"}
{"level":"INFO","timestamp":"2025-07-01T22:57:02.547+0800","caller":"utils/logger.go:94","msg":"任务处理器检查通过","taskType":"screenshot_C"}
{"level":"INFO","timestamp":"2025-07-01T22:57:02.547+0800","caller":"utils/logger.go:94","msg":"任务创建成功","taskID":"screenshot_C_1751381822547132900_test-11200","taskType":"screenshot_C","mode":"C","userName":"test-11200","roundNumber":0,"timeout":40}
{"level":"INFO","timestamp":"2025-07-01T22:57:02.547+0800","caller":"utils/logger.go:94","msg":"任务提交成功","taskID":"screenshot_C_1751381822547132900_test-11200","taskType":"screenshot_C","userName":"test-11200","priority":2}
{"level":"INFO","timestamp":"2025-07-01T22:57:02.547+0800","caller":"utils/logger.go:94","msg":"开始处理任务","workerID":1,"taskID":"screenshot_C_1751381822547132900_test-11200","taskType":"screenshot_C","userName":"test-11200","mode":"C","roundNumber":0}
{"level":"INFO","timestamp":"2025-07-01T22:57:02.547+0800","caller":"utils/logger.go:94","msg":"成功提交快捷键C模式截图任务 - TaskID: screenshot_C_1751381822547132900_test-11200, User: test-11200, Round: 0"}
{"level":"INFO","timestamp":"2025-07-01T22:57:02.547+0800","caller":"utils/logger.go:94","msg":"开始处理任务","taskID":"screenshot_C_1751381822547132900_test-11200","taskType":"screenshot_C","workerID":1}
{"level":"INFO","timestamp":"2025-07-01T22:57:02.548+0800","caller":"utils/logger.go:94","msg":"截图任务已提交到任务管理器: 快捷键C模式截图"}
{"level":"INFO","timestamp":"2025-07-01T22:57:02.548+0800","caller":"utils/logger.go:94","msg":"执行任务处理器","taskID":"screenshot_C_1751381822547132900_test-11200","attempt":1}
{"level":"INFO","timestamp":"2025-07-01T22:57:02.548+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"执行截图任务-C","patient":"test-11200","site_id":""}
{"level":"INFO","timestamp":"2025-07-01T22:57:02.548+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T22:57:02.768+0800","caller":"utils/logger.go:94","msg":"[操作:快捷键截图-模式C] [当前受检者:test-11200] [网点:YL-BJ-TZ-001] [轮次:R01]"}
{"level":"INFO","timestamp":"2025-07-01T22:57:02.768+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取待检测候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T22:57:02.954+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"当前没有选中受检者，处于本系统操作者自行研究模式","patient":"暂无候检者","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T22:57:02.954+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"处理截图工作流程","patient":"test-11200","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T22:57:02.954+0800","caller":"utils/logger.go:94","msg":"开始处理截图工作流程 - 模式: C, 用户: test-11200\n"}
{"level":"INFO","timestamp":"2025-07-01T22:57:03.357+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T22:57:03.544+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"图片OCR识别","patient":"test-11200","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T22:57:21.670+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"快捷键截图-模式B","patient":"","site_id":""}
{"level":"INFO","timestamp":"2025-07-01T22:57:21.670+0800","caller":"utils/logger.go:94","msg":"快捷键触发截图任务 - 模式: B"}
{"level":"INFO","timestamp":"2025-07-01T22:57:21.670+0800","caller":"utils/logger.go:94","msg":"收到截图任务请求: 快捷键B模式截图 (模式: B, 任务类型: screenshot_B)"}
{"level":"INFO","timestamp":"2025-07-01T22:57:21.670+0800","caller":"utils/logger.go:94","msg":"任务管理器可用，提交任务到队列"}
{"level":"INFO","timestamp":"2025-07-01T22:57:21.670+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T22:57:21.905+0800","caller":"utils/logger.go:94","msg":"收到任务提交请求","taskType":"screenshot_B","mode":"B","userName":"test-11200","roundNumber":0,"priority":"\u0002"}
{"level":"INFO","timestamp":"2025-07-01T22:57:21.905+0800","caller":"utils/logger.go:94","msg":"任务处理器检查通过","taskType":"screenshot_B"}
{"level":"ERROR","timestamp":"2025-07-01T22:57:21.905+0800","caller":"utils/logger.go:83","msg":"操作错误","operation":"提交快捷键B模式截图任务失败","patient":"test-11200","error":"task screenshot_B for user test-11200 round 0 is already running","stacktrace":"MagneticOperator/app/utils.LogError\n\tF:/myHbuilderAPP/MagneticOperator/app/utils/logger.go:83\nmain.(*App).SubmitScreenshotTask\n\tF:/myHbuilderAPP/MagneticOperator/app.go:430\nMagneticOperator/app/services.(*HotkeyService).handleScreenshot\n\tF:/myHbuilderAPP/MagneticOperator/app/services/hotkey_service.go:168\nMagneticOperator/app/services.(*HotkeyService).checkHotkeys\n\tF:/myHbuilderAPP/MagneticOperator/app/services/hotkey_service.go:132\nMagneticOperator/app/services.(*HotkeyService).StartHotkeyListener.func1\n\tF:/myHbuilderAPP/MagneticOperator/app/services/hotkey_service.go:81"}
{"level":"INFO","timestamp":"2025-07-01T22:57:21.905+0800","caller":"utils/logger.go:94","msg":"截图任务已提交到任务管理器: 快捷键B模式截图"}
{"level":"INFO","timestamp":"2025-07-01T22:57:27.892+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"快捷键截图-模式C","patient":"","site_id":""}
{"level":"INFO","timestamp":"2025-07-01T22:57:27.892+0800","caller":"utils/logger.go:94","msg":"快捷键触发截图任务 - 模式: C"}
{"level":"INFO","timestamp":"2025-07-01T22:57:27.892+0800","caller":"utils/logger.go:94","msg":"收到截图任务请求: 快捷键C模式截图 (模式: C, 任务类型: screenshot_C)"}
{"level":"INFO","timestamp":"2025-07-01T22:57:27.893+0800","caller":"utils/logger.go:94","msg":"任务管理器可用，提交任务到队列"}
{"level":"INFO","timestamp":"2025-07-01T22:57:27.893+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T22:57:28.251+0800","caller":"utils/logger.go:94","msg":"OCR API返回值详情 - 校验后器官名称: 腹部第1腰椎水平截面, 键值对数量: 40, 置信度: 0.00, 图片路径: pic\\temp\\temp_screenshot_B_test-11200_20250701_225659.png\n"}
{"level":"INFO","timestamp":"2025-07-01T22:57:28.538+0800","caller":"utils/logger.go:94","msg":"颜色检测成功完成","tempFilePath":"pic\\temp\\temp_screenshot_B_test-11200_20250701_225659.png"}
{"level":"INFO","timestamp":"2025-07-01T22:57:28.545+0800","caller":"utils/logger.go:94","msg":"开始第四步：提取D值列表数据","patientName":"test-11200","mode":"B"}
{"level":"INFO","timestamp":"2025-07-01T22:57:28.549+0800","caller":"utils/logger.go:94","msg":"提取D值列表成功","dataCount":0}
{"level":"INFO","timestamp":"2025-07-01T22:57:28.549+0800","caller":"utils/logger.go:94","msg":"开始更新用户检测信息","userName":"test-11200","mode":"B","imagePath":"pic\\temp\\temp_screenshot_B_test-11200_20250701_225659.png","organName":"腹部第1腰椎水平截面","operationID":"test-11200_B_1751381848549339000"}
{"level":"INFO","timestamp":"2025-07-01T22:57:28.553+0800","caller":"utils/logger.go:94","msg":"更新操作状态","status":"B02生化平衡分析已加入队列，等待C03病理形态学分析启动...","mode":"B","round":1,"completed":true}
{"level":"INFO","timestamp":"2025-07-01T22:57:28.553+0800","caller":"utils/logger.go:94","msg":"轮次数据更新","userName":"test-11200","currentRound":1,"mode":"B","organName":"腹部第1腰椎水平截面","oldCompletedRounds":0,"newCompletedRounds":0,"totalRounds":10,"roundsDataLength":1,"operationID":"test-11200_B_1751381848549339000"}
{"level":"INFO","timestamp":"2025-07-01T22:57:29.130+0800","caller":"utils/logger.go:94","msg":"收到任务提交请求","taskType":"screenshot_C","mode":"C","userName":"test-11200","roundNumber":0,"priority":"\u0002"}
{"level":"INFO","timestamp":"2025-07-01T22:57:29.130+0800","caller":"utils/logger.go:94","msg":"任务处理器检查通过","taskType":"screenshot_C"}
{"level":"ERROR","timestamp":"2025-07-01T22:57:29.130+0800","caller":"utils/logger.go:83","msg":"操作错误","operation":"提交快捷键C模式截图任务失败","patient":"test-11200","error":"task screenshot_C for user test-11200 round 0 is already running","stacktrace":"MagneticOperator/app/utils.LogError\n\tF:/myHbuilderAPP/MagneticOperator/app/utils/logger.go:83\nmain.(*App).SubmitScreenshotTask\n\tF:/myHbuilderAPP/MagneticOperator/app.go:430\nMagneticOperator/app/services.(*HotkeyService).handleScreenshot\n\tF:/myHbuilderAPP/MagneticOperator/app/services/hotkey_service.go:168\nMagneticOperator/app/services.(*HotkeyService).checkHotkeys\n\tF:/myHbuilderAPP/MagneticOperator/app/services/hotkey_service.go:132\nMagneticOperator/app/services.(*HotkeyService).StartHotkeyListener.func1\n\tF:/myHbuilderAPP/MagneticOperator/app/services/hotkey_service.go:81"}
{"level":"INFO","timestamp":"2025-07-01T22:57:29.131+0800","caller":"utils/logger.go:94","msg":"截图任务已提交到任务管理器: 快捷键C模式截图"}
{"level":"INFO","timestamp":"2025-07-01T22:57:55.187+0800","caller":"utils/logger.go:94","msg":"OCR API返回值详情 - 校验后器官名称: 腹部第1腰椎水平截面, 键值对数量: 1, 置信度: 0.00, 图片路径: pic\\temp\\temp_screenshot_C_test-11200_20250701_225703.png\n"}
{"level":"INFO","timestamp":"2025-07-01T22:57:55.187+0800","caller":"utils/logger.go:94","msg":"开始第四步：提取D值列表数据","patientName":"test-11200","mode":"C"}
{"level":"INFO","timestamp":"2025-07-01T22:57:55.190+0800","caller":"utils/logger.go:94","msg":"提取D值列表成功","dataCount":0}
{"level":"INFO","timestamp":"2025-07-01T22:57:55.190+0800","caller":"utils/logger.go:94","msg":"开始更新用户检测信息","userName":"test-11200","mode":"C","imagePath":"pic\\temp\\temp_screenshot_C_test-11200_20250701_225703.png","organName":"腹部第1腰椎水平截面","operationID":"test-11200_C_1751381875190448800"}
{"level":"INFO","timestamp":"2025-07-01T23:01:46.678+0800","caller":"utils/logger.go:94","msg":"清理已完成任务","remaining":0}
{"level":"INFO","timestamp":"2025-07-01T23:02:55.941+0800","caller":"utils/logger.go:94","msg":"应用程序启动","version":"1.0.0"}
{"level":"INFO","timestamp":"2025-07-01T23:02:55.941+0800","caller":"utils/logger.go:94","msg":"启动磁感分析操作台"}
{"level":"INFO","timestamp":"2025-07-01T23:02:55.941+0800","caller":"utils/logger.go:94","msg":"开始单实例检查"}
{"level":"INFO","timestamp":"2025-07-01T23:02:55.941+0800","caller":"utils/logger.go:94","msg":"检查现有实例..."}
{"level":"INFO","timestamp":"2025-07-01T23:02:55.941+0800","caller":"utils/logger.go:94","msg":"锁文件路径","path":"C:\\Users\\<USER>\\AppData\\Local\\Temp\\MagneticOperator.lock"}
{"level":"INFO","timestamp":"2025-07-01T23:02:55.941+0800","caller":"utils/logger.go:94","msg":"未找到现有锁文件"}
{"level":"INFO","timestamp":"2025-07-01T23:02:55.941+0800","caller":"utils/logger.go:94","msg":"尝试创建新锁文件..."}
{"level":"INFO","timestamp":"2025-07-01T23:02:55.941+0800","caller":"utils/logger.go:94","msg":"写入当前PID到锁文件","pid":49024}
{"level":"INFO","timestamp":"2025-07-01T23:02:55.945+0800","caller":"utils/logger.go:94","msg":"成功创建锁文件，允许启动"}
{"level":"INFO","timestamp":"2025-07-01T23:02:55.945+0800","caller":"utils/logger.go:94","msg":"单实例检查通过"}
{"level":"INFO","timestamp":"2025-07-01T23:02:55.946+0800","caller":"utils/logger.go:94","msg":"创建App实例..."}
{"level":"INFO","timestamp":"2025-07-01T23:02:55.946+0800","caller":"utils/logger.go:94","msg":"App实例创建成功"}
{"level":"INFO","timestamp":"2025-07-01T23:02:55.946+0800","caller":"utils/logger.go:94","msg":"开始启动Wails应用..."}
{"level":"INFO","timestamp":"2025-07-01T23:02:55.975+0800","caller":"utils/logger.go:94","msg":"Wails.Run()调用完成"}
{"level":"INFO","timestamp":"2025-07-01T23:02:55.975+0800","caller":"utils/logger.go:94","msg":"Wails应用正常退出"}
{"level":"INFO","timestamp":"2025-07-01T23:02:55.975+0800","caller":"utils/logger.go:94","msg":"清理锁文件..."}
{"level":"INFO","timestamp":"2025-07-01T23:02:55.975+0800","caller":"utils/logger.go:94","msg":"关闭锁文件句柄"}
{"level":"INFO","timestamp":"2025-07-01T23:02:55.975+0800","caller":"utils/logger.go:94","msg":"成功删除锁文件","path":"C:\\Users\\<USER>\\AppData\\Local\\Temp\\MagneticOperator.lock"}
{"level":"INFO","timestamp":"2025-07-01T23:02:56.087+0800","caller":"utils/logger.go:94","msg":"GetConfig called - SiteInfo: 北京市通州区潞城镇潞城社区卫生服务中心 (YL-BJ-TZ-001)"}
{"level":"INFO","timestamp":"2025-07-01T23:02:56.089+0800","caller":"utils/logger.go:94","msg":"GetConfig called - SiteInfo: 北京市通州区潞城镇潞城社区卫生服务中心 (YL-BJ-TZ-001)"}
{"level":"INFO","timestamp":"2025-07-01T23:02:56.091+0800","caller":"utils/logger.go:94","msg":"GetConfig called - SiteInfo: 北京市通州区潞城镇潞城社区卫生服务中心 (YL-BJ-TZ-001)"}
{"level":"INFO","timestamp":"2025-07-01T23:02:56.092+0800","caller":"utils/logger.go:94","msg":"GetSiteInfo called - 开始获取站点信息"}
{"level":"INFO","timestamp":"2025-07-01T23:02:56.092+0800","caller":"utils/logger.go:94","msg":"使用缓存的站点信息"}
{"level":"INFO","timestamp":"2025-07-01T23:02:56.092+0800","caller":"utils/logger.go:94","msg":"GetSiteInfo - 成功获取站点信息: ID=YL-BJ-TZ-001, Name=北京市通州区潞城镇潞城社区卫生服务中心"}
{"level":"INFO","timestamp":"2025-07-01T23:02:56.093+0800","caller":"utils/logger.go:94","msg":"GetSiteInfo called - 开始获取站点信息"}
{"level":"INFO","timestamp":"2025-07-01T23:02:56.093+0800","caller":"utils/logger.go:94","msg":"使用缓存的站点信息"}
{"level":"INFO","timestamp":"2025-07-01T23:02:56.093+0800","caller":"utils/logger.go:94","msg":"GetSiteInfo - 成功获取站点信息: ID=YL-BJ-TZ-001, Name=北京市通州区潞城镇潞城社区卫生服务中心"}
{"level":"INFO","timestamp":"2025-07-01T23:02:56.093+0800","caller":"utils/logger.go:94","msg":"GetSiteInfo called - 开始获取站点信息"}
{"level":"INFO","timestamp":"2025-07-01T23:02:56.093+0800","caller":"utils/logger.go:94","msg":"使用缓存的站点信息"}
{"level":"INFO","timestamp":"2025-07-01T23:02:56.093+0800","caller":"utils/logger.go:94","msg":"GetSiteInfo - 成功获取站点信息: ID=YL-BJ-TZ-001, Name=北京市通州区潞城镇潞城社区卫生服务中心"}
{"level":"INFO","timestamp":"2025-07-01T23:02:56.097+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T23:02:56.098+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T23:02:56.098+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T23:02:56.708+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取待检测候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T23:02:56.708+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取指定日期已完成患者列表","patient":"2025-07-01","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T23:02:56.708+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取未分析患者列表","patient":"2025-07-01","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T23:02:56.716+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取待检测候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T23:02:56.716+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取指定日期已完成患者列表","patient":"2025-07-01","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T23:02:56.716+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取未分析患者列表","patient":"2025-07-01","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T23:02:56.716+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取待检测候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T23:02:56.716+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取指定日期已完成患者列表","patient":"2025-07-01","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T23:02:56.716+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取未分析患者列表","patient":"2025-07-01","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T23:02:57.322+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"生成报到二维码","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T23:02:57.322+0800","caller":"utils/logger.go:94","msg":"复用了缓存的报到二维码"}
{"level":"INFO","timestamp":"2025-07-01T23:02:57.323+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T23:02:57.326+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"生成报到二维码","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T23:02:57.327+0800","caller":"utils/logger.go:94","msg":"复用了缓存的报到二维码"}
{"level":"INFO","timestamp":"2025-07-01T23:02:57.327+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"生成报到二维码","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T23:02:57.327+0800","caller":"utils/logger.go:94","msg":"复用了缓存的报到二维码"}
{"level":"INFO","timestamp":"2025-07-01T23:02:57.329+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T23:02:57.330+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T23:02:57.507+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取待检测候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T23:02:57.548+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取待检测候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T23:02:57.552+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取待检测候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T23:02:57.693+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取指定日期已完成患者列表","patient":"2025-07-01","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T23:02:57.796+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取指定日期已完成患者列表","patient":"2025-07-01","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T23:02:57.797+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取指定日期已完成患者列表","patient":"2025-07-01","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T23:02:58.942+0800","caller":"utils/logger.go:94","msg":"应用程序启动","version":"1.0.0"}
{"level":"INFO","timestamp":"2025-07-01T23:02:58.943+0800","caller":"utils/logger.go:94","msg":"启动磁感分析操作台"}
{"level":"INFO","timestamp":"2025-07-01T23:02:58.943+0800","caller":"utils/logger.go:94","msg":"开始单实例检查"}
{"level":"INFO","timestamp":"2025-07-01T23:02:58.943+0800","caller":"utils/logger.go:94","msg":"检查现有实例..."}
{"level":"INFO","timestamp":"2025-07-01T23:02:58.943+0800","caller":"utils/logger.go:94","msg":"锁文件路径","path":"F:\\myHbuilderAPP\\MagneticOperator\\build\\bin\\MagneticOperator.lock"}
{"level":"INFO","timestamp":"2025-07-01T23:02:58.943+0800","caller":"utils/logger.go:94","msg":"发现现有锁文件","size":5,"modified":"2025-07-01T22:56:45.422+0800"}
{"level":"INFO","timestamp":"2025-07-01T23:02:58.943+0800","caller":"utils/logger.go:94","msg":"锁文件内容","pid":"23160"}
{"level":"INFO","timestamp":"2025-07-01T23:02:58.943+0800","caller":"utils/logger.go:94","msg":"检查进程是否运行","pid":23160}
{"level":"INFO","timestamp":"2025-07-01T23:02:58.943+0800","caller":"utils/logger.go:94","msg":"检查进程是否运行","pid":23160}
{"level":"WARN","timestamp":"2025-07-01T23:02:58.943+0800","caller":"utils/logger.go:101","msg":"查找进程失败","pid":23160,"error":"OpenProcess: The parameter is incorrect."}
{"level":"INFO","timestamp":"2025-07-01T23:02:58.944+0800","caller":"utils/logger.go:94","msg":"进程未找到，清理旧锁文件","pid":23160}
{"level":"INFO","timestamp":"2025-07-01T23:02:58.944+0800","caller":"utils/logger.go:94","msg":"成功删除旧锁文件"}
{"level":"INFO","timestamp":"2025-07-01T23:02:58.944+0800","caller":"utils/logger.go:94","msg":"尝试创建新锁文件..."}
{"level":"INFO","timestamp":"2025-07-01T23:02:58.944+0800","caller":"utils/logger.go:94","msg":"写入当前PID到锁文件","pid":46956}
{"level":"INFO","timestamp":"2025-07-01T23:02:59.017+0800","caller":"utils/logger.go:94","msg":"成功创建锁文件，允许启动"}
{"level":"INFO","timestamp":"2025-07-01T23:02:59.018+0800","caller":"utils/logger.go:94","msg":"单实例检查通过"}
{"level":"INFO","timestamp":"2025-07-01T23:02:59.018+0800","caller":"utils/logger.go:94","msg":"创建App实例..."}
{"level":"INFO","timestamp":"2025-07-01T23:02:59.018+0800","caller":"utils/logger.go:94","msg":"App实例创建成功"}
{"level":"INFO","timestamp":"2025-07-01T23:02:59.018+0800","caller":"utils/logger.go:94","msg":"开始启动Wails应用..."}
{"level":"INFO","timestamp":"2025-07-01T23:02:59.465+0800","caller":"utils/logger.go:94","msg":"=== STARTUP函数被调用 ==="}
{"level":"INFO","timestamp":"2025-07-01T23:02:59.465+0800","caller":"utils/logger.go:94","msg":"日志系统初始化成功"}
{"level":"INFO","timestamp":"2025-07-01T23:02:59.466+0800","caller":"utils/logger.go:94","msg":"消息配置系统初始化成功"}
{"level":"INFO","timestamp":"2025-07-01T23:02:59.466+0800","caller":"utils/logger.go:94","msg":"开始初始化服务..."}
{"level":"INFO","timestamp":"2025-07-01T23:02:59.466+0800","caller":"utils/logger.go:94","msg":"开始调用 initServices()..."}
{"level":"INFO","timestamp":"2025-07-01T23:02:59.466+0800","caller":"utils/logger.go:94","msg":"开始初始化服务..."}
{"level":"INFO","timestamp":"2025-07-01T23:02:59.472+0800","caller":"utils/logger.go:94","msg":"成功加载配置文件"}
{"level":"INFO","timestamp":"2025-07-01T23:02:59.474+0800","caller":"utils/logger.go:94","msg":"集成并发截图服务初始化成功"}
{"level":"INFO","timestamp":"2025-07-01T23:02:59.474+0800","caller":"utils/logger.go:94","msg":"开始初始化任务管理器..."}
{"level":"INFO","timestamp":"2025-07-01T23:02:59.474+0800","caller":"utils/logger.go:94","msg":"开始创建任务处理器..."}
{"level":"INFO","timestamp":"2025-07-01T23:02:59.474+0800","caller":"utils/logger.go:94","msg":"截图任务处理器创建完成"}
{"level":"INFO","timestamp":"2025-07-01T23:02:59.474+0800","caller":"utils/logger.go:94","msg":"OCR任务处理器创建完成"}
{"level":"INFO","timestamp":"2025-07-01T23:02:59.474+0800","caller":"utils/logger.go:94","msg":"上传任务处理器创建完成"}
{"level":"INFO","timestamp":"2025-07-01T23:02:59.474+0800","caller":"utils/logger.go:94","msg":"开始注册任务处理器..."}
{"level":"INFO","timestamp":"2025-07-01T23:02:59.474+0800","caller":"utils/logger.go:94","msg":"任务处理器注册成功","taskType":"screenshot_A"}
{"level":"INFO","timestamp":"2025-07-01T23:02:59.475+0800","caller":"utils/logger.go:94","msg":"截图A任务处理器注册成功"}
{"level":"INFO","timestamp":"2025-07-01T23:02:59.475+0800","caller":"utils/logger.go:94","msg":"任务处理器注册成功","taskType":"screenshot_B"}
{"level":"INFO","timestamp":"2025-07-01T23:02:59.475+0800","caller":"utils/logger.go:94","msg":"截图B任务处理器注册成功"}
{"level":"INFO","timestamp":"2025-07-01T23:02:59.475+0800","caller":"utils/logger.go:94","msg":"任务处理器注册成功","taskType":"screenshot_C"}
{"level":"INFO","timestamp":"2025-07-01T23:02:59.475+0800","caller":"utils/logger.go:94","msg":"截图C任务处理器注册成功"}
{"level":"INFO","timestamp":"2025-07-01T23:02:59.476+0800","caller":"utils/logger.go:94","msg":"任务处理器注册成功","taskType":"ocr_process"}
{"level":"INFO","timestamp":"2025-07-01T23:02:59.476+0800","caller":"utils/logger.go:94","msg":"OCR任务处理器注册成功"}
{"level":"INFO","timestamp":"2025-07-01T23:02:59.476+0800","caller":"utils/logger.go:94","msg":"任务处理器注册成功","taskType":"upload"}
{"level":"INFO","timestamp":"2025-07-01T23:02:59.476+0800","caller":"utils/logger.go:94","msg":"上传任务处理器注册成功"}
{"level":"INFO","timestamp":"2025-07-01T23:02:59.476+0800","caller":"utils/logger.go:94","msg":"开始启动任务管理器..."}
{"level":"INFO","timestamp":"2025-07-01T23:02:59.476+0800","caller":"utils/logger.go:94","msg":"任务管理器启动成功","maxConcurrent":3,"registeredHandlers":5,"cooldownPeriod":0.5}
{"level":"INFO","timestamp":"2025-07-01T23:02:59.476+0800","caller":"utils/logger.go:94","msg":"启动工作协程","workerCount":3}
{"level":"INFO","timestamp":"2025-07-01T23:02:59.476+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":0}
{"level":"INFO","timestamp":"2025-07-01T23:02:59.476+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":0}
{"level":"INFO","timestamp":"2025-07-01T23:02:59.476+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":1}
{"level":"INFO","timestamp":"2025-07-01T23:02:59.476+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":1}
{"level":"INFO","timestamp":"2025-07-01T23:02:59.477+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":2}
{"level":"INFO","timestamp":"2025-07-01T23:02:59.477+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":2}
{"level":"INFO","timestamp":"2025-07-01T23:02:59.477+0800","caller":"utils/logger.go:94","msg":"清理协程启动成功"}
{"level":"INFO","timestamp":"2025-07-01T23:02:59.477+0800","caller":"utils/logger.go:94","msg":"任务管理器启动事件已发送"}
{"level":"INFO","timestamp":"2025-07-01T23:02:59.477+0800","caller":"utils/logger.go:94","msg":"任务管理器启动成功"}
{"level":"INFO","timestamp":"2025-07-01T23:02:59.477+0800","caller":"utils/logger.go:94","msg":"任务管理器初始化成功"}
{"level":"INFO","timestamp":"2025-07-01T23:02:59.478+0800","caller":"utils/logger.go:94","msg":"开始从API加载站点信息..."}
{"level":"INFO","timestamp":"2025-07-01T23:02:59.685+0800","caller":"utils/logger.go:94","msg":"GetConfig called - SiteInfo: 北京市通州区潞城镇潞城社区卫生服务中心 (YL-BJ-TZ-001)"}
{"level":"INFO","timestamp":"2025-07-01T23:02:59.752+0800","caller":"utils/logger.go:94","msg":"DOM准备就绪，开始设置窗口位置和尺寸"}
{"level":"INFO","timestamp":"2025-07-01T23:02:59.762+0800","caller":"utils/logger.go:94","msg":"GetSiteInfo called - 开始获取站点信息"}
{"level":"INFO","timestamp":"2025-07-01T23:02:59.762+0800","caller":"utils/logger.go:94","msg":"使用缓存的站点信息"}
{"level":"INFO","timestamp":"2025-07-01T23:02:59.762+0800","caller":"utils/logger.go:94","msg":"GetSiteInfo - 成功获取站点信息: ID=YL-BJ-TZ-001, Name=北京市通州区潞城镇潞城社区卫生服务中心"}
{"level":"INFO","timestamp":"2025-07-01T23:02:59.773+0800","caller":"utils/logger.go:94","msg":"窗口位置设置为紧凑模式: x=2944, y=748, width=512, height=806"}
{"level":"INFO","timestamp":"2025-07-01T23:02:59.777+0800","caller":"utils/logger.go:94","msg":"窗体已设置为置顶"}
{"level":"INFO","timestamp":"2025-07-01T23:02:59.777+0800","caller":"utils/logger.go:94","msg":"窗体已显示"}
{"level":"INFO","timestamp":"2025-07-01T23:02:59.789+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T23:02:59.957+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取待检测候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T23:02:59.957+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取指定日期已完成患者列表","patient":"2025-07-01","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T23:02:59.957+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取未分析患者列表","patient":"2025-07-01","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T23:03:00.115+0800","caller":"utils/logger.go:94","msg":"站点信息无变化，复用现有二维码"}
{"level":"INFO","timestamp":"2025-07-01T23:03:00.115+0800","caller":"utils/logger.go:94","msg":"initServices() 完成"}
{"level":"INFO","timestamp":"2025-07-01T23:03:00.116+0800","caller":"utils/logger.go:94","msg":"开始创建并发截图服务..."}
{"level":"INFO","timestamp":"2025-07-01T23:03:00.116+0800","caller":"utils/logger.go:94","msg":"并发截图服务创建完成"}
{"level":"INFO","timestamp":"2025-07-01T23:03:00.116+0800","caller":"utils/logger.go:94","msg":"窗体状态初始化完成"}
{"level":"INFO","timestamp":"2025-07-01T23:03:00.116+0800","caller":"utils/logger.go:94","msg":"开始创建必要的目录..."}
{"level":"INFO","timestamp":"2025-07-01T23:03:00.116+0800","caller":"utils/logger.go:94","msg":"pic目录创建成功"}
{"level":"INFO","timestamp":"2025-07-01T23:03:00.116+0800","caller":"utils/logger.go:94","msg":"config目录创建成功"}
{"level":"INFO","timestamp":"2025-07-01T23:03:00.116+0800","caller":"utils/logger.go:94","msg":"开始启动快捷键监听..."}
{"level":"INFO","timestamp":"2025-07-01T23:03:00.116+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"启动快捷键监听","patient":"","site_id":""}
{"level":"INFO","timestamp":"2025-07-01T23:03:00.116+0800","caller":"utils/logger.go:94","msg":"快捷键服务启动成功"}
{"level":"INFO","timestamp":"2025-07-01T23:03:00.117+0800","caller":"utils/logger.go:94","msg":"启动OCR任务清理定时器..."}
{"level":"INFO","timestamp":"2025-07-01T23:03:00.117+0800","caller":"utils/logger.go:94","msg":"OCR任务清理定时器启动成功"}
{"level":"INFO","timestamp":"2025-07-01T23:03:00.117+0800","caller":"utils/logger.go:94","msg":"应用启动成功"}
{"level":"INFO","timestamp":"2025-07-01T23:03:00.242+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"生成报到二维码","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T23:03:00.242+0800","caller":"utils/logger.go:94","msg":"复用了缓存的报到二维码"}
{"level":"INFO","timestamp":"2025-07-01T23:03:00.248+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T23:03:00.450+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取待检测候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T23:03:00.633+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取指定日期已完成患者列表","patient":"2025-07-01","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T23:03:31.829+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"快捷键截图-模式B","patient":"","site_id":""}
{"level":"INFO","timestamp":"2025-07-01T23:03:31.829+0800","caller":"utils/logger.go:94","msg":"快捷键触发截图任务 - 模式: B"}
{"level":"INFO","timestamp":"2025-07-01T23:03:31.830+0800","caller":"utils/logger.go:94","msg":"收到截图任务请求: 快捷键B模式截图 (模式: B, 任务类型: screenshot_B)"}
{"level":"INFO","timestamp":"2025-07-01T23:03:31.830+0800","caller":"utils/logger.go:94","msg":"任务管理器可用，提交任务到队列"}
{"level":"INFO","timestamp":"2025-07-01T23:03:31.830+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T23:03:32.135+0800","caller":"utils/logger.go:94","msg":"收到任务提交请求","taskType":"screenshot_B","mode":"B","userName":"test-11200","roundNumber":0,"priority":"\u0002"}
{"level":"INFO","timestamp":"2025-07-01T23:03:32.135+0800","caller":"utils/logger.go:94","msg":"任务处理器检查通过","taskType":"screenshot_B"}
{"level":"INFO","timestamp":"2025-07-01T23:03:32.136+0800","caller":"utils/logger.go:94","msg":"任务创建成功","taskID":"screenshot_B_1751382212135734000_test-11200","taskType":"screenshot_B","mode":"B","userName":"test-11200","roundNumber":0,"timeout":35}
{"level":"INFO","timestamp":"2025-07-01T23:03:32.136+0800","caller":"utils/logger.go:94","msg":"任务提交成功","taskID":"screenshot_B_1751382212135734000_test-11200","taskType":"screenshot_B","userName":"test-11200","priority":2}
{"level":"INFO","timestamp":"2025-07-01T23:03:32.136+0800","caller":"utils/logger.go:94","msg":"开始处理任务","workerID":0,"taskID":"screenshot_B_1751382212135734000_test-11200","taskType":"screenshot_B","userName":"test-11200","mode":"B","roundNumber":0}
{"level":"INFO","timestamp":"2025-07-01T23:03:32.136+0800","caller":"utils/logger.go:94","msg":"开始处理任务","taskID":"screenshot_B_1751382212135734000_test-11200","taskType":"screenshot_B","workerID":0}
{"level":"INFO","timestamp":"2025-07-01T23:03:32.136+0800","caller":"utils/logger.go:94","msg":"成功提交快捷键B模式截图任务 - TaskID: screenshot_B_1751382212135734000_test-11200, User: test-11200, Round: 0"}
{"level":"INFO","timestamp":"2025-07-01T23:03:32.136+0800","caller":"utils/logger.go:94","msg":"执行任务处理器","taskID":"screenshot_B_1751382212135734000_test-11200","attempt":1}
{"level":"INFO","timestamp":"2025-07-01T23:03:32.136+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"执行截图任务-B","patient":"test-11200","site_id":""}
{"level":"INFO","timestamp":"2025-07-01T23:03:32.136+0800","caller":"utils/logger.go:94","msg":"截图任务已提交到任务管理器: 快捷键B模式截图"}
{"level":"INFO","timestamp":"2025-07-01T23:03:32.136+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T23:03:32.365+0800","caller":"utils/logger.go:94","msg":"[操作:快捷键截图-模式B] [当前受检者:test-11200] [网点:YL-BJ-TZ-001] [轮次:R01]"}
{"level":"INFO","timestamp":"2025-07-01T23:03:32.365+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取待检测候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T23:03:32.582+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"当前没有选中受检者，处于本系统操作者自行研究模式","patient":"暂无候检者","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T23:03:32.582+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"处理截图工作流程","patient":"test-11200","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T23:03:32.582+0800","caller":"utils/logger.go:94","msg":"开始处理截图工作流程 - 模式: B, 用户: test-11200\n"}
{"level":"INFO","timestamp":"2025-07-01T23:03:33.020+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T23:03:33.200+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"图片OCR识别","patient":"test-11200","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T23:03:37.067+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"快捷键截图-模式C","patient":"","site_id":""}
{"level":"INFO","timestamp":"2025-07-01T23:03:37.067+0800","caller":"utils/logger.go:94","msg":"快捷键触发截图任务 - 模式: C"}
{"level":"INFO","timestamp":"2025-07-01T23:03:37.068+0800","caller":"utils/logger.go:94","msg":"收到截图任务请求: 快捷键C模式截图 (模式: C, 任务类型: screenshot_C)"}
{"level":"INFO","timestamp":"2025-07-01T23:03:37.068+0800","caller":"utils/logger.go:94","msg":"任务管理器可用，提交任务到队列"}
{"level":"INFO","timestamp":"2025-07-01T23:03:37.068+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T23:03:37.276+0800","caller":"utils/logger.go:94","msg":"收到任务提交请求","taskType":"screenshot_C","mode":"C","userName":"test-11200","roundNumber":0,"priority":"\u0002"}
{"level":"INFO","timestamp":"2025-07-01T23:03:37.276+0800","caller":"utils/logger.go:94","msg":"任务处理器检查通过","taskType":"screenshot_C"}
{"level":"INFO","timestamp":"2025-07-01T23:03:37.276+0800","caller":"utils/logger.go:94","msg":"任务创建成功","taskID":"screenshot_C_1751382217276990800_test-11200","taskType":"screenshot_C","mode":"C","userName":"test-11200","roundNumber":0,"timeout":40}
{"level":"INFO","timestamp":"2025-07-01T23:03:37.276+0800","caller":"utils/logger.go:94","msg":"任务提交成功","taskID":"screenshot_C_1751382217276990800_test-11200","taskType":"screenshot_C","userName":"test-11200","priority":2}
{"level":"INFO","timestamp":"2025-07-01T23:03:37.276+0800","caller":"utils/logger.go:94","msg":"开始处理任务","workerID":1,"taskID":"screenshot_C_1751382217276990800_test-11200","taskType":"screenshot_C","userName":"test-11200","mode":"C","roundNumber":0}
{"level":"INFO","timestamp":"2025-07-01T23:03:37.276+0800","caller":"utils/logger.go:94","msg":"成功提交快捷键C模式截图任务 - TaskID: screenshot_C_1751382217276990800_test-11200, User: test-11200, Round: 0"}
{"level":"INFO","timestamp":"2025-07-01T23:03:37.276+0800","caller":"utils/logger.go:94","msg":"开始处理任务","taskID":"screenshot_C_1751382217276990800_test-11200","taskType":"screenshot_C","workerID":1}
{"level":"INFO","timestamp":"2025-07-01T23:03:37.277+0800","caller":"utils/logger.go:94","msg":"执行任务处理器","taskID":"screenshot_C_1751382217276990800_test-11200","attempt":1}
{"level":"INFO","timestamp":"2025-07-01T23:03:37.277+0800","caller":"utils/logger.go:94","msg":"截图任务已提交到任务管理器: 快捷键C模式截图"}
{"level":"INFO","timestamp":"2025-07-01T23:03:37.277+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"执行截图任务-C","patient":"test-11200","site_id":""}
{"level":"INFO","timestamp":"2025-07-01T23:03:37.278+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T23:03:37.480+0800","caller":"utils/logger.go:94","msg":"[操作:快捷键截图-模式C] [当前受检者:test-11200] [网点:YL-BJ-TZ-001] [轮次:R01]"}
{"level":"INFO","timestamp":"2025-07-01T23:03:37.480+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取待检测候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T23:03:37.658+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"当前没有选中受检者，处于本系统操作者自行研究模式","patient":"暂无候检者","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T23:03:37.658+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"处理截图工作流程","patient":"test-11200","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T23:03:37.659+0800","caller":"utils/logger.go:94","msg":"开始处理截图工作流程 - 模式: C, 用户: test-11200\n"}
{"level":"INFO","timestamp":"2025-07-01T23:03:38.057+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T23:03:38.298+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"图片OCR识别","patient":"test-11200","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T23:03:55.596+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"快捷键截图-模式B","patient":"","site_id":""}
{"level":"INFO","timestamp":"2025-07-01T23:03:55.596+0800","caller":"utils/logger.go:94","msg":"快捷键触发截图任务 - 模式: B"}
{"level":"INFO","timestamp":"2025-07-01T23:03:55.596+0800","caller":"utils/logger.go:94","msg":"收到截图任务请求: 快捷键B模式截图 (模式: B, 任务类型: screenshot_B)"}
{"level":"INFO","timestamp":"2025-07-01T23:03:55.597+0800","caller":"utils/logger.go:94","msg":"任务管理器可用，提交任务到队列"}
{"level":"INFO","timestamp":"2025-07-01T23:03:55.597+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T23:03:55.869+0800","caller":"utils/logger.go:94","msg":"收到任务提交请求","taskType":"screenshot_B","mode":"B","userName":"test-11200","roundNumber":0,"priority":"\u0002"}
{"level":"INFO","timestamp":"2025-07-01T23:03:55.869+0800","caller":"utils/logger.go:94","msg":"任务处理器检查通过","taskType":"screenshot_B"}
{"level":"ERROR","timestamp":"2025-07-01T23:03:55.870+0800","caller":"utils/logger.go:83","msg":"操作错误","operation":"提交快捷键B模式截图任务失败","patient":"test-11200","error":"task screenshot_B for user test-11200 round 0 is already running","stacktrace":"MagneticOperator/app/utils.LogError\n\tF:/myHbuilderAPP/MagneticOperator/app/utils/logger.go:83\nmain.(*App).SubmitScreenshotTask\n\tF:/myHbuilderAPP/MagneticOperator/app.go:430\nMagneticOperator/app/services.(*HotkeyService).handleScreenshot\n\tF:/myHbuilderAPP/MagneticOperator/app/services/hotkey_service.go:168\nMagneticOperator/app/services.(*HotkeyService).checkHotkeys\n\tF:/myHbuilderAPP/MagneticOperator/app/services/hotkey_service.go:132\nMagneticOperator/app/services.(*HotkeyService).StartHotkeyListener.func1\n\tF:/myHbuilderAPP/MagneticOperator/app/services/hotkey_service.go:81"}
{"level":"INFO","timestamp":"2025-07-01T23:03:55.870+0800","caller":"utils/logger.go:94","msg":"截图任务已提交到任务管理器: 快捷键B模式截图"}
{"level":"INFO","timestamp":"2025-07-01T23:03:59.545+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"快捷键截图-模式C","patient":"","site_id":""}
{"level":"INFO","timestamp":"2025-07-01T23:03:59.546+0800","caller":"utils/logger.go:94","msg":"快捷键触发截图任务 - 模式: C"}
{"level":"INFO","timestamp":"2025-07-01T23:03:59.546+0800","caller":"utils/logger.go:94","msg":"收到截图任务请求: 快捷键C模式截图 (模式: C, 任务类型: screenshot_C)"}
{"level":"INFO","timestamp":"2025-07-01T23:03:59.546+0800","caller":"utils/logger.go:94","msg":"任务管理器可用，提交任务到队列"}
{"level":"INFO","timestamp":"2025-07-01T23:03:59.546+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T23:03:59.803+0800","caller":"utils/logger.go:94","msg":"收到任务提交请求","taskType":"screenshot_C","mode":"C","userName":"test-11200","roundNumber":0,"priority":"\u0002"}
{"level":"INFO","timestamp":"2025-07-01T23:03:59.803+0800","caller":"utils/logger.go:94","msg":"任务处理器检查通过","taskType":"screenshot_C"}
{"level":"ERROR","timestamp":"2025-07-01T23:03:59.804+0800","caller":"utils/logger.go:83","msg":"操作错误","operation":"提交快捷键C模式截图任务失败","patient":"test-11200","error":"task screenshot_C for user test-11200 round 0 is already running","stacktrace":"MagneticOperator/app/utils.LogError\n\tF:/myHbuilderAPP/MagneticOperator/app/utils/logger.go:83\nmain.(*App).SubmitScreenshotTask\n\tF:/myHbuilderAPP/MagneticOperator/app.go:430\nMagneticOperator/app/services.(*HotkeyService).handleScreenshot\n\tF:/myHbuilderAPP/MagneticOperator/app/services/hotkey_service.go:168\nMagneticOperator/app/services.(*HotkeyService).checkHotkeys\n\tF:/myHbuilderAPP/MagneticOperator/app/services/hotkey_service.go:132\nMagneticOperator/app/services.(*HotkeyService).StartHotkeyListener.func1\n\tF:/myHbuilderAPP/MagneticOperator/app/services/hotkey_service.go:81"}
{"level":"INFO","timestamp":"2025-07-01T23:03:59.804+0800","caller":"utils/logger.go:94","msg":"截图任务已提交到任务管理器: 快捷键C模式截图"}
{"level":"INFO","timestamp":"2025-07-01T23:04:02.701+0800","caller":"utils/logger.go:94","msg":"OCR API返回值详情 - 校验后器官名称: 消化系统--胰腺；十二指肠；正面图, 键值对数量: 28, 置信度: 0.00, 图片路径: pic\\temp\\temp_screenshot_B_test-11200_20250701_230332.png\n"}
{"level":"INFO","timestamp":"2025-07-01T23:04:03.018+0800","caller":"utils/logger.go:94","msg":"颜色检测成功完成","tempFilePath":"pic\\temp\\temp_screenshot_B_test-11200_20250701_230332.png"}
{"level":"INFO","timestamp":"2025-07-01T23:04:03.026+0800","caller":"utils/logger.go:94","msg":"开始第四步：提取D值列表数据","patientName":"test-11200","mode":"B"}
{"level":"INFO","timestamp":"2025-07-01T23:04:03.028+0800","caller":"utils/logger.go:94","msg":"提取D值列表成功","dataCount":0}
{"level":"INFO","timestamp":"2025-07-01T23:04:03.028+0800","caller":"utils/logger.go:94","msg":"开始更新用户检测信息","userName":"test-11200","mode":"B","imagePath":"pic\\temp\\temp_screenshot_B_test-11200_20250701_230332.png","organName":"消化系统--胰腺；十二指肠；正面图","operationID":"test-11200_B_1751382243028649700"}
{"level":"INFO","timestamp":"2025-07-01T23:04:03.032+0800","caller":"utils/logger.go:94","msg":"更新操作状态","status":"B02生化平衡分析已加入队列，等待C03病理形态学分析启动...","mode":"B","round":1,"completed":true}
{"level":"INFO","timestamp":"2025-07-01T23:04:03.032+0800","caller":"utils/logger.go:94","msg":"轮次数据更新","userName":"test-11200","currentRound":1,"mode":"B","organName":"消化系统--胰腺；十二指肠；正面图","oldCompletedRounds":0,"newCompletedRounds":0,"totalRounds":10,"roundsDataLength":1,"operationID":"test-11200_B_1751382243028649700"}
{"level":"INFO","timestamp":"2025-07-01T23:04:28.689+0800","caller":"utils/logger.go:94","msg":"OCR API返回值详情 - 校验后器官名称: 消化系统--胰腺；十二指肠；正面图, 键值对数量: 1, 置信度: 0.00, 图片路径: pic\\temp\\temp_screenshot_C_test-11200_20250701_230337.png\n"}
{"level":"INFO","timestamp":"2025-07-01T23:04:28.690+0800","caller":"utils/logger.go:94","msg":"开始第四步：提取D值列表数据","patientName":"test-11200","mode":"C"}
{"level":"INFO","timestamp":"2025-07-01T23:04:28.692+0800","caller":"utils/logger.go:94","msg":"提取D值列表成功","dataCount":0}
{"level":"INFO","timestamp":"2025-07-01T23:04:28.692+0800","caller":"utils/logger.go:94","msg":"开始更新用户检测信息","userName":"test-11200","mode":"C","imagePath":"pic\\temp\\temp_screenshot_C_test-11200_20250701_230337.png","organName":"消化系统--胰腺；十二指肠；正面图","operationID":"test-11200_C_1751382268692770100"}
{"level":"INFO","timestamp":"2025-07-01T23:07:59.477+0800","caller":"utils/logger.go:94","msg":"清理已完成任务","remaining":0}
{"level":"INFO","timestamp":"2025-07-01T23:12:59.477+0800","caller":"utils/logger.go:94","msg":"清理已完成任务","remaining":0}
{"level":"INFO","timestamp":"2025-07-01T23:17:59.477+0800","caller":"utils/logger.go:94","msg":"清理已完成任务","remaining":0}
{"level":"INFO","timestamp":"2025-07-01T23:22:05.084+0800","caller":"utils/logger.go:94","msg":"收到退出信号，开始清理..."}
{"level":"INFO","timestamp":"2025-07-01T23:22:05.084+0800","caller":"utils/logger.go:94","msg":"应用开始关闭，执行清理工作..."}
{"level":"INFO","timestamp":"2025-07-01T23:22:05.084+0800","caller":"utils/logger.go:94","msg":"清理锁文件..."}
{"level":"INFO","timestamp":"2025-07-01T23:22:05.084+0800","caller":"utils/logger.go:94","msg":"关闭锁文件句柄"}
{"level":"INFO","timestamp":"2025-07-01T23:22:05.084+0800","caller":"utils/logger.go:94","msg":"成功删除锁文件","path":"F:\\myHbuilderAPP\\MagneticOperator\\build\\bin\\MagneticOperator.lock"}
{"level":"INFO","timestamp":"2025-07-01T23:22:05.084+0800","caller":"utils/logger.go:94","msg":"清理完成，程序退出"}
{"level":"INFO","timestamp":"2025-07-01T23:30:18.082+0800","caller":"utils/logger.go:94","msg":"应用程序启动","version":"1.0.0"}
{"level":"INFO","timestamp":"2025-07-01T23:30:18.082+0800","caller":"utils/logger.go:94","msg":"启动磁感分析操作台"}
{"level":"INFO","timestamp":"2025-07-01T23:30:18.082+0800","caller":"utils/logger.go:94","msg":"开始单实例检查"}
{"level":"INFO","timestamp":"2025-07-01T23:30:18.082+0800","caller":"utils/logger.go:94","msg":"检查现有实例..."}
{"level":"INFO","timestamp":"2025-07-01T23:30:18.082+0800","caller":"utils/logger.go:94","msg":"锁文件路径","path":"C:\\Users\\<USER>\\AppData\\Local\\Temp\\MagneticOperator.lock"}
{"level":"INFO","timestamp":"2025-07-01T23:30:18.082+0800","caller":"utils/logger.go:94","msg":"未找到现有锁文件"}
{"level":"INFO","timestamp":"2025-07-01T23:30:18.082+0800","caller":"utils/logger.go:94","msg":"尝试创建新锁文件..."}
{"level":"INFO","timestamp":"2025-07-01T23:30:18.082+0800","caller":"utils/logger.go:94","msg":"写入当前PID到锁文件","pid":25112}
{"level":"INFO","timestamp":"2025-07-01T23:30:18.104+0800","caller":"utils/logger.go:94","msg":"成功创建锁文件，允许启动"}
{"level":"INFO","timestamp":"2025-07-01T23:30:18.104+0800","caller":"utils/logger.go:94","msg":"单实例检查通过"}
{"level":"INFO","timestamp":"2025-07-01T23:30:18.104+0800","caller":"utils/logger.go:94","msg":"创建App实例..."}
{"level":"INFO","timestamp":"2025-07-01T23:30:18.104+0800","caller":"utils/logger.go:94","msg":"App实例创建成功"}
{"level":"INFO","timestamp":"2025-07-01T23:30:18.104+0800","caller":"utils/logger.go:94","msg":"开始启动Wails应用..."}
{"level":"INFO","timestamp":"2025-07-01T23:30:18.112+0800","caller":"utils/logger.go:94","msg":"Wails.Run()调用完成"}
{"level":"INFO","timestamp":"2025-07-01T23:30:18.112+0800","caller":"utils/logger.go:94","msg":"Wails应用正常退出"}
{"level":"INFO","timestamp":"2025-07-01T23:30:18.112+0800","caller":"utils/logger.go:94","msg":"清理锁文件..."}
{"level":"INFO","timestamp":"2025-07-01T23:30:18.112+0800","caller":"utils/logger.go:94","msg":"关闭锁文件句柄"}
{"level":"INFO","timestamp":"2025-07-01T23:30:18.113+0800","caller":"utils/logger.go:94","msg":"成功删除锁文件","path":"C:\\Users\\<USER>\\AppData\\Local\\Temp\\MagneticOperator.lock"}
{"level":"INFO","timestamp":"2025-07-01T23:30:33.303+0800","caller":"utils/logger.go:94","msg":"应用程序启动","version":"1.0.0"}
2025-07-01 23:30:33.303687 +0800 CST m=+0.021747901 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-01T23:30:33.317+0800","caller":"utils/logger.go:94","msg":"启动磁感分析操作台"}
2025-07-01 23:30:33.3178868 +0800 CST m=+0.035947701 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-01T23:30:33.328+0800","caller":"utils/logger.go:94","msg":"开始单实例检查"}
2025-07-01 23:30:33.3286616 +0800 CST m=+0.046722501 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-01T23:30:33.340+0800","caller":"utils/logger.go:94","msg":"检查现有实例..."}
2025-07-01 23:30:33.3401871 +0800 CST m=+0.058248001 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-01T23:30:33.351+0800","caller":"utils/logger.go:94","msg":"锁文件路径","path":"F:\\myHbuilderAPP\\MagneticOperator\\build\\bin\\MagneticOperator.lock"}
2025-07-01 23:30:33.3510266 +0800 CST m=+0.069087501 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-01T23:30:33.375+0800","caller":"utils/logger.go:94","msg":"未找到现有锁文件"}
2025-07-01 23:30:33.375292 +0800 CST m=+0.093352901 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-01T23:30:33.397+0800","caller":"utils/logger.go:94","msg":"尝试创建新锁文件..."}
2025-07-01 23:30:33.3973993 +0800 CST m=+0.115460201 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-01T23:30:33.419+0800","caller":"utils/logger.go:94","msg":"写入当前PID到锁文件","pid":22828}
2025-07-01 23:30:33.4196514 +0800 CST m=+0.137712301 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-01T23:30:33.474+0800","caller":"utils/logger.go:94","msg":"成功创建锁文件，允许启动"}
2025-07-01 23:30:33.4740198 +0800 CST m=+0.192080701 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-01T23:30:33.497+0800","caller":"utils/logger.go:94","msg":"单实例检查通过"}
2025-07-01 23:30:33.4971264 +0800 CST m=+0.215187301 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-01T23:30:33.519+0800","caller":"utils/logger.go:94","msg":"创建App实例..."}
2025-07-01 23:30:33.5197404 +0800 CST m=+0.237801301 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-01T23:30:33.541+0800","caller":"utils/logger.go:94","msg":"App实例创建成功"}
2025-07-01 23:30:33.5419716 +0800 CST m=+0.260032501 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-01T23:30:33.564+0800","caller":"utils/logger.go:94","msg":"开始启动Wails应用..."}
2025-07-01 23:30:33.5641221 +0800 CST m=+0.282183001 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-01T23:30:34.062+0800","caller":"utils/logger.go:94","msg":"=== STARTUP函数被调用 ==="}
2025-07-01 23:30:34.0629004 +0800 CST m=+0.780961301 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-01T23:30:34.086+0800","caller":"utils/logger.go:94","msg":"日志系统初始化成功"}
2025-07-01 23:30:34.0866257 +0800 CST m=+0.804686601 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-01T23:30:34.108+0800","caller":"utils/logger.go:94","msg":"消息配置系统初始化成功"}
2025-07-01 23:30:34.108734 +0800 CST m=+0.826794901 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-01T23:30:34.130+0800","caller":"utils/logger.go:94","msg":"开始初始化服务..."}
2025-07-01 23:30:34.1308921 +0800 CST m=+0.848953001 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-01T23:30:34.153+0800","caller":"utils/logger.go:94","msg":"开始调用 initServices()..."}
2025-07-01 23:30:34.1531726 +0800 CST m=+0.871233501 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-01T23:30:34.174+0800","caller":"utils/logger.go:94","msg":"开始初始化服务..."}
2025-07-01 23:30:34.174938 +0800 CST m=+0.892998901 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-01T23:30:34.202+0800","caller":"utils/logger.go:94","msg":"成功加载配置文件"}
2025-07-01 23:30:34.2022764 +0800 CST m=+0.920337301 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-01T23:30:34.218+0800","caller":"utils/logger.go:94","msg":"集成并发截图服务初始化成功"}
2025-07-01 23:30:34.2184208 +0800 CST m=+0.936481701 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-01T23:30:34.242+0800","caller":"utils/logger.go:94","msg":"开始初始化任务管理器..."}
2025-07-01 23:30:34.2422256 +0800 CST m=+0.960286501 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-01T23:30:34.264+0800","caller":"utils/logger.go:94","msg":"开始创建任务处理器..."}
2025-07-01 23:30:34.2640062 +0800 CST m=+0.982067101 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-01T23:30:34.273+0800","caller":"utils/logger.go:94","msg":"截图任务处理器创建完成"}
2025-07-01 23:30:34.2738133 +0800 CST m=+0.991874201 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-01T23:30:34.276+0800","caller":"utils/logger.go:94","msg":"GetConfig called - SiteInfo: 北京市通州区潞城镇潞城社区卫生服务中心 (YL-BJ-TZ-001)"}
2025-07-01 23:30:34.2767377 +0800 CST m=+0.994798601 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-01T23:30:34.307+0800","caller":"utils/logger.go:94","msg":"OCR任务处理器创建完成"}
2025-07-01 23:30:34.3076377 +0800 CST m=+1.025698601 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-01T23:30:34.329+0800","caller":"utils/logger.go:94","msg":"上传任务处理器创建完成"}
2025-07-01 23:30:34.3298203 +0800 CST m=+1.047881201 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-01T23:30:34.334+0800","caller":"utils/logger.go:94","msg":"DOM准备就绪，开始设置窗口位置和尺寸"}
2025-07-01 23:30:34.3347201 +0800 CST m=+1.052781001 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-01T23:30:34.340+0800","caller":"utils/logger.go:94","msg":"开始注册任务处理器..."}
{"level":"INFO","timestamp":"2025-07-01T23:30:34.344+0800","caller":"utils/logger.go:94","msg":"GetSiteInfo called - 开始获取站点信息"}
2025-07-01 23:30:34.340672 +0800 CST m=+1.058732901 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-01T23:30:34.360+0800","caller":"utils/logger.go:94","msg":"窗口位置设置为紧凑模式: x=2944, y=748, width=512, height=806"}
2025-07-01 23:30:34.3448004 +0800 CST m=+1.062861301 write error: write /dev/stdout: The handle is invalid.
2025-07-01 23:30:34.3603949 +0800 CST m=+1.078455801 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-01T23:30:34.363+0800","caller":"utils/logger.go:94","msg":"任务处理器注册成功","taskType":"screenshot_A"}
{"level":"INFO","timestamp":"2025-07-01T23:30:34.374+0800","caller":"utils/logger.go:94","msg":"使用缓存的站点信息"}
2025-07-01 23:30:34.3630253 +0800 CST m=+1.081086201 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-01T23:30:34.377+0800","caller":"utils/logger.go:94","msg":"窗体已设置为置顶"}
2025-07-01 23:30:34.374394 +0800 CST m=+1.092454901 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-01T23:30:34.385+0800","caller":"utils/logger.go:94","msg":"截图A任务处理器注册成功"}
2025-07-01 23:30:34.37705 +0800 CST m=+1.095110901 write error: write /dev/stdout: The handle is invalid.
2025-07-01 23:30:34.3857674 +0800 CST m=+1.103828301 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-01T23:30:34.397+0800","caller":"utils/logger.go:94","msg":"GetSiteInfo - 成功获取站点信息: ID=YL-BJ-TZ-001, Name=北京市通州区潞城镇潞城社区卫生服务中心"}
{"level":"INFO","timestamp":"2025-07-01T23:30:34.407+0800","caller":"utils/logger.go:94","msg":"任务处理器注册成功","taskType":"screenshot_B"}
2025-07-01 23:30:34.3970856 +0800 CST m=+1.115146501 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-01T23:30:34.408+0800","caller":"utils/logger.go:94","msg":"窗体已显示"}
2025-07-01 23:30:34.4077258 +0800 CST m=+1.125786701 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-01T23:30:34.422+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
2025-07-01 23:30:34.4082287 +0800 CST m=+1.126289601 write error: write /dev/stdout: The handle is invalid.
2025-07-01 23:30:34.4223802 +0800 CST m=+1.140441101 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-01T23:30:34.430+0800","caller":"utils/logger.go:94","msg":"截图B任务处理器注册成功"}
2025-07-01 23:30:34.430462 +0800 CST m=+1.148522901 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-01T23:30:34.452+0800","caller":"utils/logger.go:94","msg":"任务处理器注册成功","taskType":"screenshot_C"}
2025-07-01 23:30:34.4521872 +0800 CST m=+1.170248101 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-01T23:30:34.463+0800","caller":"utils/logger.go:94","msg":"截图C任务处理器注册成功"}
2025-07-01 23:30:34.4638628 +0800 CST m=+1.181923701 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-01T23:30:34.474+0800","caller":"utils/logger.go:94","msg":"任务处理器注册成功","taskType":"ocr_process"}
2025-07-01 23:30:34.4746893 +0800 CST m=+1.192750201 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-01T23:30:34.486+0800","caller":"utils/logger.go:94","msg":"OCR任务处理器注册成功"}
2025-07-01 23:30:34.486159 +0800 CST m=+1.204219901 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-01T23:30:34.496+0800","caller":"utils/logger.go:94","msg":"任务处理器注册成功","taskType":"upload"}
2025-07-01 23:30:34.4969569 +0800 CST m=+1.215017801 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-01T23:30:34.508+0800","caller":"utils/logger.go:94","msg":"上传任务处理器注册成功"}
2025-07-01 23:30:34.5085185 +0800 CST m=+1.226579401 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-01T23:30:34.519+0800","caller":"utils/logger.go:94","msg":"开始启动任务管理器..."}
2025-07-01 23:30:34.519423 +0800 CST m=+1.237483901 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-01T23:30:34.530+0800","caller":"utils/logger.go:94","msg":"任务管理器启动成功","maxConcurrent":3,"registeredHandlers":5,"cooldownPeriod":0.5}
2025-07-01 23:30:34.5309327 +0800 CST m=+1.248993601 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-01T23:30:34.541+0800","caller":"utils/logger.go:94","msg":"启动工作协程","workerCount":3}
2025-07-01 23:30:34.5417539 +0800 CST m=+1.259814801 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-01T23:30:34.553+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":0}
{"level":"INFO","timestamp":"2025-07-01T23:30:34.553+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":0}
2025-07-01 23:30:34.5531303 +0800 CST m=+1.271191201 write error: write /dev/stdout: The handle is invalid.
2025-07-01 23:30:34.5531303 +0800 CST m=+1.271191201 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-01T23:30:34.575+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":1}
{"level":"INFO","timestamp":"2025-07-01T23:30:34.575+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":1}
2025-07-01 23:30:34.5755058 +0800 CST m=+1.293566701 write error: write /dev/stdout: The handle is invalid.
2025-07-01 23:30:34.5755058 +0800 CST m=+1.293566701 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-01T23:30:34.597+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":2}
{"level":"INFO","timestamp":"2025-07-01T23:30:34.597+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":2}
2025-07-01 23:30:34.5978496 +0800 CST m=+1.315910501 write error: write /dev/stdout: The handle is invalid.
2025-07-01 23:30:34.5978496 +0800 CST m=+1.315910501 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-01T23:30:34.608+0800","caller":"utils/logger.go:94","msg":"清理协程启动成功"}
2025-07-01 23:30:34.6088064 +0800 CST m=+1.326867301 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-01T23:30:34.631+0800","caller":"utils/logger.go:94","msg":"任务管理器启动事件已发送"}
2025-07-01 23:30:34.6312755 +0800 CST m=+1.349336401 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-01T23:30:34.642+0800","caller":"utils/logger.go:94","msg":"任务管理器启动成功"}
2025-07-01 23:30:34.6420431 +0800 CST m=+1.360104001 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-01T23:30:34.653+0800","caller":"utils/logger.go:94","msg":"任务管理器初始化成功"}
2025-07-01 23:30:34.6535343 +0800 CST m=+1.371595201 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-01T23:30:34.664+0800","caller":"utils/logger.go:94","msg":"开始从API加载站点信息..."}
2025-07-01 23:30:34.6647098 +0800 CST m=+1.382770701 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-01T23:31:50.807+0800","caller":"utils/logger.go:94","msg":"应用开始关闭，执行清理工作..."}
2025-07-01 23:31:50.8074727 +0800 CST m=+77.525533601 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-01T23:31:51.183+0800","caller":"utils/logger.go:94","msg":"快捷键服务已停止"}
2025-07-01 23:31:51.1832083 +0800 CST m=+77.901269201 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-01T23:31:51.194+0800","caller":"utils/logger.go:94","msg":"集成截图服务已关闭"}
2025-07-01 23:31:51.1942824 +0800 CST m=+77.912343301 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-01T23:31:51.205+0800","caller":"utils/logger.go:94","msg":"OCR服务已关闭"}
2025-07-01 23:31:51.2051079 +0800 CST m=+77.923168801 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-01T23:31:51.216+0800","caller":"utils/logger.go:94","msg":"应用清理工作完成"}
2025-07-01 23:31:51.2163615 +0800 CST m=+77.934422401 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-01T23:31:51.248+0800","caller":"utils/logger.go:94","msg":"Wails.Run()调用完成"}
2025-07-01 23:31:51.2489439 +0800 CST m=+77.967004801 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-01T23:31:51.261+0800","caller":"utils/logger.go:94","msg":"Wails应用正常退出"}
2025-07-01 23:31:51.2611308 +0800 CST m=+77.979191701 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-01T23:31:51.272+0800","caller":"utils/logger.go:94","msg":"清理锁文件..."}
2025-07-01 23:31:51.2723331 +0800 CST m=+77.990394001 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-01T23:31:51.283+0800","caller":"utils/logger.go:94","msg":"关闭锁文件句柄"}
2025-07-01 23:31:51.2831797 +0800 CST m=+78.001240601 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-01T23:31:51.294+0800","caller":"utils/logger.go:94","msg":"成功删除锁文件","path":"F:\\myHbuilderAPP\\MagneticOperator\\build\\bin\\MagneticOperator.lock"}
2025-07-01 23:31:51.2946097 +0800 CST m=+78.012670601 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-01T23:32:08.205+0800","caller":"utils/logger.go:94","msg":"应用程序启动","version":"1.0.0"}
{"level":"INFO","timestamp":"2025-07-01T23:32:08.205+0800","caller":"utils/logger.go:94","msg":"启动磁感分析操作台"}
{"level":"INFO","timestamp":"2025-07-01T23:32:08.205+0800","caller":"utils/logger.go:94","msg":"开始单实例检查"}
{"level":"INFO","timestamp":"2025-07-01T23:32:08.205+0800","caller":"utils/logger.go:94","msg":"检查现有实例..."}
{"level":"INFO","timestamp":"2025-07-01T23:32:08.205+0800","caller":"utils/logger.go:94","msg":"锁文件路径","path":"C:\\Users\\<USER>\\AppData\\Local\\Temp\\MagneticOperator.lock"}
{"level":"INFO","timestamp":"2025-07-01T23:32:08.205+0800","caller":"utils/logger.go:94","msg":"未找到现有锁文件"}
{"level":"INFO","timestamp":"2025-07-01T23:32:08.205+0800","caller":"utils/logger.go:94","msg":"尝试创建新锁文件..."}
{"level":"INFO","timestamp":"2025-07-01T23:32:08.206+0800","caller":"utils/logger.go:94","msg":"写入当前PID到锁文件","pid":6900}
{"level":"INFO","timestamp":"2025-07-01T23:32:08.226+0800","caller":"utils/logger.go:94","msg":"成功创建锁文件，允许启动"}
{"level":"INFO","timestamp":"2025-07-01T23:32:08.227+0800","caller":"utils/logger.go:94","msg":"单实例检查通过"}
{"level":"INFO","timestamp":"2025-07-01T23:32:08.227+0800","caller":"utils/logger.go:94","msg":"创建App实例..."}
{"level":"INFO","timestamp":"2025-07-01T23:32:08.227+0800","caller":"utils/logger.go:94","msg":"App实例创建成功"}
{"level":"INFO","timestamp":"2025-07-01T23:32:08.227+0800","caller":"utils/logger.go:94","msg":"开始启动Wails应用..."}
{"level":"INFO","timestamp":"2025-07-01T23:32:08.234+0800","caller":"utils/logger.go:94","msg":"Wails.Run()调用完成"}
{"level":"INFO","timestamp":"2025-07-01T23:32:08.234+0800","caller":"utils/logger.go:94","msg":"Wails应用正常退出"}
{"level":"INFO","timestamp":"2025-07-01T23:32:08.234+0800","caller":"utils/logger.go:94","msg":"清理锁文件..."}
{"level":"INFO","timestamp":"2025-07-01T23:32:08.234+0800","caller":"utils/logger.go:94","msg":"关闭锁文件句柄"}
{"level":"INFO","timestamp":"2025-07-01T23:32:08.235+0800","caller":"utils/logger.go:94","msg":"成功删除锁文件","path":"C:\\Users\\<USER>\\AppData\\Local\\Temp\\MagneticOperator.lock"}
{"level":"INFO","timestamp":"2025-07-01T23:32:14.064+0800","caller":"utils/logger.go:94","msg":"应用程序启动","version":"1.0.0"}
{"level":"INFO","timestamp":"2025-07-01T23:32:14.064+0800","caller":"utils/logger.go:94","msg":"启动磁感分析操作台"}
{"level":"INFO","timestamp":"2025-07-01T23:32:14.064+0800","caller":"utils/logger.go:94","msg":"开始单实例检查"}
{"level":"INFO","timestamp":"2025-07-01T23:32:14.064+0800","caller":"utils/logger.go:94","msg":"检查现有实例..."}
{"level":"INFO","timestamp":"2025-07-01T23:32:14.064+0800","caller":"utils/logger.go:94","msg":"锁文件路径","path":"C:\\Users\\<USER>\\AppData\\Local\\Temp\\MagneticOperator.lock"}
{"level":"INFO","timestamp":"2025-07-01T23:32:14.064+0800","caller":"utils/logger.go:94","msg":"未找到现有锁文件"}
{"level":"INFO","timestamp":"2025-07-01T23:32:14.064+0800","caller":"utils/logger.go:94","msg":"尝试创建新锁文件..."}
{"level":"INFO","timestamp":"2025-07-01T23:32:14.065+0800","caller":"utils/logger.go:94","msg":"写入当前PID到锁文件","pid":1108}
{"level":"INFO","timestamp":"2025-07-01T23:32:14.089+0800","caller":"utils/logger.go:94","msg":"成功创建锁文件，允许启动"}
{"level":"INFO","timestamp":"2025-07-01T23:32:14.089+0800","caller":"utils/logger.go:94","msg":"单实例检查通过"}
{"level":"INFO","timestamp":"2025-07-01T23:32:14.089+0800","caller":"utils/logger.go:94","msg":"创建App实例..."}
{"level":"INFO","timestamp":"2025-07-01T23:32:14.089+0800","caller":"utils/logger.go:94","msg":"App实例创建成功"}
{"level":"INFO","timestamp":"2025-07-01T23:32:14.089+0800","caller":"utils/logger.go:94","msg":"开始启动Wails应用..."}
{"level":"INFO","timestamp":"2025-07-01T23:32:14.098+0800","caller":"utils/logger.go:94","msg":"Wails.Run()调用完成"}
{"level":"INFO","timestamp":"2025-07-01T23:32:14.098+0800","caller":"utils/logger.go:94","msg":"Wails应用正常退出"}
{"level":"INFO","timestamp":"2025-07-01T23:32:14.098+0800","caller":"utils/logger.go:94","msg":"清理锁文件..."}
{"level":"INFO","timestamp":"2025-07-01T23:32:14.098+0800","caller":"utils/logger.go:94","msg":"关闭锁文件句柄"}
{"level":"INFO","timestamp":"2025-07-01T23:32:14.098+0800","caller":"utils/logger.go:94","msg":"成功删除锁文件","path":"C:\\Users\\<USER>\\AppData\\Local\\Temp\\MagneticOperator.lock"}
{"level":"INFO","timestamp":"2025-07-01T23:32:16.972+0800","caller":"utils/logger.go:94","msg":"应用程序启动","version":"1.0.0"}
{"level":"INFO","timestamp":"2025-07-01T23:32:16.973+0800","caller":"utils/logger.go:94","msg":"启动磁感分析操作台"}
{"level":"INFO","timestamp":"2025-07-01T23:32:16.973+0800","caller":"utils/logger.go:94","msg":"开始单实例检查"}
{"level":"INFO","timestamp":"2025-07-01T23:32:16.973+0800","caller":"utils/logger.go:94","msg":"检查现有实例..."}
{"level":"INFO","timestamp":"2025-07-01T23:32:16.973+0800","caller":"utils/logger.go:94","msg":"锁文件路径","path":"F:\\myHbuilderAPP\\MagneticOperator\\build\\bin\\MagneticOperator.lock"}
{"level":"INFO","timestamp":"2025-07-01T23:32:16.973+0800","caller":"utils/logger.go:94","msg":"未找到现有锁文件"}
{"level":"INFO","timestamp":"2025-07-01T23:32:16.973+0800","caller":"utils/logger.go:94","msg":"尝试创建新锁文件..."}
{"level":"INFO","timestamp":"2025-07-01T23:32:16.974+0800","caller":"utils/logger.go:94","msg":"写入当前PID到锁文件","pid":47416}
{"level":"INFO","timestamp":"2025-07-01T23:32:17.047+0800","caller":"utils/logger.go:94","msg":"成功创建锁文件，允许启动"}
{"level":"INFO","timestamp":"2025-07-01T23:32:17.047+0800","caller":"utils/logger.go:94","msg":"单实例检查通过"}
{"level":"INFO","timestamp":"2025-07-01T23:32:17.047+0800","caller":"utils/logger.go:94","msg":"创建App实例..."}
{"level":"INFO","timestamp":"2025-07-01T23:32:17.047+0800","caller":"utils/logger.go:94","msg":"App实例创建成功"}
{"level":"INFO","timestamp":"2025-07-01T23:32:17.047+0800","caller":"utils/logger.go:94","msg":"开始启动Wails应用..."}
{"level":"INFO","timestamp":"2025-07-01T23:32:17.472+0800","caller":"utils/logger.go:94","msg":"=== STARTUP函数被调用 ==="}
{"level":"INFO","timestamp":"2025-07-01T23:32:17.473+0800","caller":"utils/logger.go:94","msg":"日志系统初始化成功"}
{"level":"INFO","timestamp":"2025-07-01T23:32:17.474+0800","caller":"utils/logger.go:94","msg":"消息配置系统初始化成功"}
{"level":"INFO","timestamp":"2025-07-01T23:32:17.474+0800","caller":"utils/logger.go:94","msg":"开始初始化服务..."}
{"level":"INFO","timestamp":"2025-07-01T23:32:17.474+0800","caller":"utils/logger.go:94","msg":"开始调用 initServices()..."}
{"level":"INFO","timestamp":"2025-07-01T23:32:17.474+0800","caller":"utils/logger.go:94","msg":"开始初始化服务..."}
{"level":"INFO","timestamp":"2025-07-01T23:32:17.479+0800","caller":"utils/logger.go:94","msg":"成功加载配置文件"}
{"level":"INFO","timestamp":"2025-07-01T23:32:17.481+0800","caller":"utils/logger.go:94","msg":"集成并发截图服务初始化成功"}
{"level":"INFO","timestamp":"2025-07-01T23:32:17.481+0800","caller":"utils/logger.go:94","msg":"开始初始化任务管理器..."}
{"level":"INFO","timestamp":"2025-07-01T23:32:17.482+0800","caller":"utils/logger.go:94","msg":"开始创建任务处理器..."}
{"level":"INFO","timestamp":"2025-07-01T23:32:17.482+0800","caller":"utils/logger.go:94","msg":"截图任务处理器创建完成"}
{"level":"INFO","timestamp":"2025-07-01T23:32:17.482+0800","caller":"utils/logger.go:94","msg":"OCR任务处理器创建完成"}
{"level":"INFO","timestamp":"2025-07-01T23:32:17.482+0800","caller":"utils/logger.go:94","msg":"上传任务处理器创建完成"}
{"level":"INFO","timestamp":"2025-07-01T23:32:17.482+0800","caller":"utils/logger.go:94","msg":"开始注册任务处理器..."}
{"level":"INFO","timestamp":"2025-07-01T23:32:17.482+0800","caller":"utils/logger.go:94","msg":"任务处理器注册成功","taskType":"screenshot_A"}
{"level":"INFO","timestamp":"2025-07-01T23:32:17.482+0800","caller":"utils/logger.go:94","msg":"截图A任务处理器注册成功"}
{"level":"INFO","timestamp":"2025-07-01T23:32:17.483+0800","caller":"utils/logger.go:94","msg":"任务处理器注册成功","taskType":"screenshot_B"}
{"level":"INFO","timestamp":"2025-07-01T23:32:17.483+0800","caller":"utils/logger.go:94","msg":"截图B任务处理器注册成功"}
{"level":"INFO","timestamp":"2025-07-01T23:32:17.483+0800","caller":"utils/logger.go:94","msg":"任务处理器注册成功","taskType":"screenshot_C"}
{"level":"INFO","timestamp":"2025-07-01T23:32:17.483+0800","caller":"utils/logger.go:94","msg":"截图C任务处理器注册成功"}
{"level":"INFO","timestamp":"2025-07-01T23:32:17.483+0800","caller":"utils/logger.go:94","msg":"任务处理器注册成功","taskType":"ocr_process"}
{"level":"INFO","timestamp":"2025-07-01T23:32:17.483+0800","caller":"utils/logger.go:94","msg":"OCR任务处理器注册成功"}
{"level":"INFO","timestamp":"2025-07-01T23:32:17.483+0800","caller":"utils/logger.go:94","msg":"任务处理器注册成功","taskType":"upload"}
{"level":"INFO","timestamp":"2025-07-01T23:32:17.483+0800","caller":"utils/logger.go:94","msg":"上传任务处理器注册成功"}
{"level":"INFO","timestamp":"2025-07-01T23:32:17.484+0800","caller":"utils/logger.go:94","msg":"开始启动任务管理器..."}
{"level":"INFO","timestamp":"2025-07-01T23:32:17.484+0800","caller":"utils/logger.go:94","msg":"任务管理器启动成功","maxConcurrent":3,"registeredHandlers":5,"cooldownPeriod":0.5}
{"level":"INFO","timestamp":"2025-07-01T23:32:17.484+0800","caller":"utils/logger.go:94","msg":"启动工作协程","workerCount":3}
{"level":"INFO","timestamp":"2025-07-01T23:32:17.484+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":0}
{"level":"INFO","timestamp":"2025-07-01T23:32:17.484+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":0}
{"level":"INFO","timestamp":"2025-07-01T23:32:17.484+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":1}
{"level":"INFO","timestamp":"2025-07-01T23:32:17.484+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":1}
{"level":"INFO","timestamp":"2025-07-01T23:32:17.484+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":2}
{"level":"INFO","timestamp":"2025-07-01T23:32:17.484+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":2}
{"level":"INFO","timestamp":"2025-07-01T23:32:17.484+0800","caller":"utils/logger.go:94","msg":"清理协程启动成功"}
{"level":"INFO","timestamp":"2025-07-01T23:32:17.485+0800","caller":"utils/logger.go:94","msg":"任务管理器启动事件已发送"}
{"level":"INFO","timestamp":"2025-07-01T23:32:17.485+0800","caller":"utils/logger.go:94","msg":"任务管理器启动成功"}
{"level":"INFO","timestamp":"2025-07-01T23:32:17.485+0800","caller":"utils/logger.go:94","msg":"任务管理器初始化成功"}
{"level":"INFO","timestamp":"2025-07-01T23:32:17.485+0800","caller":"utils/logger.go:94","msg":"开始从API加载站点信息..."}
{"level":"INFO","timestamp":"2025-07-01T23:32:18.032+0800","caller":"utils/logger.go:94","msg":"GetConfig called - SiteInfo: 北京市通州区潞城镇潞城社区卫生服务中心 (YL-BJ-TZ-001)"}
{"level":"INFO","timestamp":"2025-07-01T23:32:18.098+0800","caller":"utils/logger.go:94","msg":"DOM准备就绪，开始设置窗口位置和尺寸"}
{"level":"INFO","timestamp":"2025-07-01T23:32:18.109+0800","caller":"utils/logger.go:94","msg":"GetSiteInfo called - 开始获取站点信息"}
{"level":"INFO","timestamp":"2025-07-01T23:32:18.109+0800","caller":"utils/logger.go:94","msg":"使用缓存的站点信息"}
{"level":"INFO","timestamp":"2025-07-01T23:32:18.109+0800","caller":"utils/logger.go:94","msg":"GetSiteInfo - 成功获取站点信息: ID=YL-BJ-TZ-001, Name=北京市通州区潞城镇潞城社区卫生服务中心"}
{"level":"INFO","timestamp":"2025-07-01T23:32:18.125+0800","caller":"utils/logger.go:94","msg":"窗口位置设置为紧凑模式: x=2944, y=748, width=512, height=806"}
{"level":"INFO","timestamp":"2025-07-01T23:32:18.129+0800","caller":"utils/logger.go:94","msg":"窗体已设置为置顶"}
{"level":"INFO","timestamp":"2025-07-01T23:32:18.129+0800","caller":"utils/logger.go:94","msg":"窗体已显示"}
{"level":"INFO","timestamp":"2025-07-01T23:32:18.135+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T23:33:13.313+0800","caller":"utils/logger.go:94","msg":"应用开始关闭，执行清理工作..."}
{"level":"INFO","timestamp":"2025-07-01T23:33:13.313+0800","caller":"utils/logger.go:94","msg":"快捷键服务已停止"}
{"level":"INFO","timestamp":"2025-07-01T23:33:13.313+0800","caller":"utils/logger.go:94","msg":"集成截图服务已关闭"}
{"level":"INFO","timestamp":"2025-07-01T23:33:13.313+0800","caller":"utils/logger.go:94","msg":"OCR服务已关闭"}
{"level":"INFO","timestamp":"2025-07-01T23:33:13.313+0800","caller":"utils/logger.go:94","msg":"应用清理工作完成"}
{"level":"INFO","timestamp":"2025-07-01T23:33:13.336+0800","caller":"utils/logger.go:94","msg":"Wails.Run()调用完成"}
{"level":"INFO","timestamp":"2025-07-01T23:33:13.336+0800","caller":"utils/logger.go:94","msg":"Wails应用正常退出"}
{"level":"INFO","timestamp":"2025-07-01T23:33:13.336+0800","caller":"utils/logger.go:94","msg":"清理锁文件..."}
{"level":"INFO","timestamp":"2025-07-01T23:33:13.336+0800","caller":"utils/logger.go:94","msg":"关闭锁文件句柄"}
{"level":"INFO","timestamp":"2025-07-01T23:33:13.336+0800","caller":"utils/logger.go:94","msg":"成功删除锁文件","path":"F:\\myHbuilderAPP\\MagneticOperator\\build\\bin\\MagneticOperator.lock"}
