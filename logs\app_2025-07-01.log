{"level":"INFO","timestamp":"2025-07-01T21:50:18.362+0800","caller":"utils/logger.go:94","msg":"应用程序启动","version":"1.0.0"}
{"level":"INFO","timestamp":"2025-07-01T21:50:18.362+0800","caller":"utils/logger.go:94","msg":"启动磁感分析操作台"}
{"level":"INFO","timestamp":"2025-07-01T21:50:18.362+0800","caller":"utils/logger.go:94","msg":"开始单实例检查"}
{"level":"INFO","timestamp":"2025-07-01T21:50:18.362+0800","caller":"utils/logger.go:94","msg":"检查现有实例..."}
{"level":"INFO","timestamp":"2025-07-01T21:50:18.362+0800","caller":"utils/logger.go:94","msg":"锁文件路径","path":"C:\\Users\\<USER>\\AppData\\Local\\Temp\\MagneticOperator.lock"}
{"level":"INFO","timestamp":"2025-07-01T21:50:18.362+0800","caller":"utils/logger.go:94","msg":"未找到现有锁文件"}
{"level":"INFO","timestamp":"2025-07-01T21:50:18.362+0800","caller":"utils/logger.go:94","msg":"尝试创建新锁文件..."}
{"level":"INFO","timestamp":"2025-07-01T21:50:18.363+0800","caller":"utils/logger.go:94","msg":"写入当前PID到锁文件","pid":52464}
{"level":"INFO","timestamp":"2025-07-01T21:50:18.384+0800","caller":"utils/logger.go:94","msg":"成功创建锁文件，允许启动"}
{"level":"INFO","timestamp":"2025-07-01T21:50:18.384+0800","caller":"utils/logger.go:94","msg":"单实例检查通过"}
{"level":"INFO","timestamp":"2025-07-01T21:50:18.384+0800","caller":"utils/logger.go:94","msg":"创建App实例..."}
{"level":"INFO","timestamp":"2025-07-01T21:50:18.384+0800","caller":"utils/logger.go:94","msg":"App实例创建成功"}
{"level":"INFO","timestamp":"2025-07-01T21:50:18.384+0800","caller":"utils/logger.go:94","msg":"开始启动Wails应用..."}
{"level":"INFO","timestamp":"2025-07-01T21:50:18.403+0800","caller":"utils/logger.go:94","msg":"Wails.Run()调用完成"}
{"level":"INFO","timestamp":"2025-07-01T21:50:18.403+0800","caller":"utils/logger.go:94","msg":"Wails应用正常退出"}
{"level":"INFO","timestamp":"2025-07-01T21:50:18.403+0800","caller":"utils/logger.go:94","msg":"清理锁文件..."}
{"level":"INFO","timestamp":"2025-07-01T21:50:18.403+0800","caller":"utils/logger.go:94","msg":"关闭锁文件句柄"}
{"level":"INFO","timestamp":"2025-07-01T21:50:18.403+0800","caller":"utils/logger.go:94","msg":"成功删除锁文件","path":"C:\\Users\\<USER>\\AppData\\Local\\Temp\\MagneticOperator.lock"}
{"level":"INFO","timestamp":"2025-07-01T22:56:34.210+0800","caller":"utils/logger.go:94","msg":"应用程序启动","version":"1.0.0"}
{"level":"INFO","timestamp":"2025-07-01T22:56:34.210+0800","caller":"utils/logger.go:94","msg":"启动磁感分析操作台"}
{"level":"INFO","timestamp":"2025-07-01T22:56:34.210+0800","caller":"utils/logger.go:94","msg":"开始单实例检查"}
{"level":"INFO","timestamp":"2025-07-01T22:56:34.210+0800","caller":"utils/logger.go:94","msg":"检查现有实例..."}
{"level":"INFO","timestamp":"2025-07-01T22:56:34.210+0800","caller":"utils/logger.go:94","msg":"锁文件路径","path":"C:\\Users\\<USER>\\AppData\\Local\\Temp\\MagneticOperator.lock"}
{"level":"INFO","timestamp":"2025-07-01T22:56:34.210+0800","caller":"utils/logger.go:94","msg":"未找到现有锁文件"}
{"level":"INFO","timestamp":"2025-07-01T22:56:34.210+0800","caller":"utils/logger.go:94","msg":"尝试创建新锁文件..."}
{"level":"INFO","timestamp":"2025-07-01T22:56:34.211+0800","caller":"utils/logger.go:94","msg":"写入当前PID到锁文件","pid":47848}
{"level":"INFO","timestamp":"2025-07-01T22:56:34.213+0800","caller":"utils/logger.go:94","msg":"成功创建锁文件，允许启动"}
{"level":"INFO","timestamp":"2025-07-01T22:56:34.213+0800","caller":"utils/logger.go:94","msg":"单实例检查通过"}
{"level":"INFO","timestamp":"2025-07-01T22:56:34.213+0800","caller":"utils/logger.go:94","msg":"创建App实例..."}
{"level":"INFO","timestamp":"2025-07-01T22:56:34.213+0800","caller":"utils/logger.go:94","msg":"App实例创建成功"}
{"level":"INFO","timestamp":"2025-07-01T22:56:34.213+0800","caller":"utils/logger.go:94","msg":"开始启动Wails应用..."}
{"level":"INFO","timestamp":"2025-07-01T22:56:34.221+0800","caller":"utils/logger.go:94","msg":"Wails.Run()调用完成"}
{"level":"INFO","timestamp":"2025-07-01T22:56:34.221+0800","caller":"utils/logger.go:94","msg":"Wails应用正常退出"}
{"level":"INFO","timestamp":"2025-07-01T22:56:34.221+0800","caller":"utils/logger.go:94","msg":"清理锁文件..."}
{"level":"INFO","timestamp":"2025-07-01T22:56:34.221+0800","caller":"utils/logger.go:94","msg":"关闭锁文件句柄"}
{"level":"INFO","timestamp":"2025-07-01T22:56:34.222+0800","caller":"utils/logger.go:94","msg":"成功删除锁文件","path":"C:\\Users\\<USER>\\AppData\\Local\\Temp\\MagneticOperator.lock"}
{"level":"INFO","timestamp":"2025-07-01T22:56:41.482+0800","caller":"utils/logger.go:94","msg":"应用程序启动","version":"1.0.0"}
{"level":"INFO","timestamp":"2025-07-01T22:56:41.482+0800","caller":"utils/logger.go:94","msg":"启动磁感分析操作台"}
{"level":"INFO","timestamp":"2025-07-01T22:56:41.482+0800","caller":"utils/logger.go:94","msg":"开始单实例检查"}
{"level":"INFO","timestamp":"2025-07-01T22:56:41.482+0800","caller":"utils/logger.go:94","msg":"检查现有实例..."}
{"level":"INFO","timestamp":"2025-07-01T22:56:41.482+0800","caller":"utils/logger.go:94","msg":"锁文件路径","path":"C:\\Users\\<USER>\\AppData\\Local\\Temp\\MagneticOperator.lock"}
{"level":"INFO","timestamp":"2025-07-01T22:56:41.482+0800","caller":"utils/logger.go:94","msg":"未找到现有锁文件"}
{"level":"INFO","timestamp":"2025-07-01T22:56:41.482+0800","caller":"utils/logger.go:94","msg":"尝试创建新锁文件..."}
{"level":"INFO","timestamp":"2025-07-01T22:56:41.483+0800","caller":"utils/logger.go:94","msg":"写入当前PID到锁文件","pid":41824}
{"level":"INFO","timestamp":"2025-07-01T22:56:41.503+0800","caller":"utils/logger.go:94","msg":"成功创建锁文件，允许启动"}
{"level":"INFO","timestamp":"2025-07-01T22:56:41.504+0800","caller":"utils/logger.go:94","msg":"单实例检查通过"}
{"level":"INFO","timestamp":"2025-07-01T22:56:41.504+0800","caller":"utils/logger.go:94","msg":"创建App实例..."}
{"level":"INFO","timestamp":"2025-07-01T22:56:41.504+0800","caller":"utils/logger.go:94","msg":"App实例创建成功"}
{"level":"INFO","timestamp":"2025-07-01T22:56:41.504+0800","caller":"utils/logger.go:94","msg":"开始启动Wails应用..."}
{"level":"INFO","timestamp":"2025-07-01T22:56:41.512+0800","caller":"utils/logger.go:94","msg":"Wails.Run()调用完成"}
{"level":"INFO","timestamp":"2025-07-01T22:56:41.512+0800","caller":"utils/logger.go:94","msg":"Wails应用正常退出"}
{"level":"INFO","timestamp":"2025-07-01T22:56:41.512+0800","caller":"utils/logger.go:94","msg":"清理锁文件..."}
{"level":"INFO","timestamp":"2025-07-01T22:56:41.512+0800","caller":"utils/logger.go:94","msg":"关闭锁文件句柄"}
{"level":"INFO","timestamp":"2025-07-01T22:56:41.512+0800","caller":"utils/logger.go:94","msg":"成功删除锁文件","path":"C:\\Users\\<USER>\\AppData\\Local\\Temp\\MagneticOperator.lock"}
{"level":"INFO","timestamp":"2025-07-01T22:56:45.421+0800","caller":"utils/logger.go:94","msg":"应用程序启动","version":"1.0.0"}
{"level":"INFO","timestamp":"2025-07-01T22:56:45.421+0800","caller":"utils/logger.go:94","msg":"启动磁感分析操作台"}
{"level":"INFO","timestamp":"2025-07-01T22:56:45.421+0800","caller":"utils/logger.go:94","msg":"开始单实例检查"}
{"level":"INFO","timestamp":"2025-07-01T22:56:45.421+0800","caller":"utils/logger.go:94","msg":"检查现有实例..."}
{"level":"INFO","timestamp":"2025-07-01T22:56:45.421+0800","caller":"utils/logger.go:94","msg":"锁文件路径","path":"F:\\myHbuilderAPP\\MagneticOperator\\build\\bin\\MagneticOperator.lock"}
{"level":"INFO","timestamp":"2025-07-01T22:56:45.421+0800","caller":"utils/logger.go:94","msg":"未找到现有锁文件"}
{"level":"INFO","timestamp":"2025-07-01T22:56:45.422+0800","caller":"utils/logger.go:94","msg":"尝试创建新锁文件..."}
{"level":"INFO","timestamp":"2025-07-01T22:56:45.422+0800","caller":"utils/logger.go:94","msg":"写入当前PID到锁文件","pid":23160}
{"level":"INFO","timestamp":"2025-07-01T22:56:45.510+0800","caller":"utils/logger.go:94","msg":"成功创建锁文件，允许启动"}
{"level":"INFO","timestamp":"2025-07-01T22:56:45.511+0800","caller":"utils/logger.go:94","msg":"单实例检查通过"}
{"level":"INFO","timestamp":"2025-07-01T22:56:45.511+0800","caller":"utils/logger.go:94","msg":"创建App实例..."}
{"level":"INFO","timestamp":"2025-07-01T22:56:45.511+0800","caller":"utils/logger.go:94","msg":"App实例创建成功"}
{"level":"INFO","timestamp":"2025-07-01T22:56:45.511+0800","caller":"utils/logger.go:94","msg":"开始启动Wails应用..."}
{"level":"INFO","timestamp":"2025-07-01T22:56:46.664+0800","caller":"utils/logger.go:94","msg":"=== STARTUP函数被调用 ==="}
{"level":"INFO","timestamp":"2025-07-01T22:56:46.665+0800","caller":"utils/logger.go:94","msg":"日志系统初始化成功"}
{"level":"INFO","timestamp":"2025-07-01T22:56:46.665+0800","caller":"utils/logger.go:94","msg":"消息配置系统初始化成功"}
{"level":"INFO","timestamp":"2025-07-01T22:56:46.666+0800","caller":"utils/logger.go:94","msg":"开始初始化服务..."}
{"level":"INFO","timestamp":"2025-07-01T22:56:46.666+0800","caller":"utils/logger.go:94","msg":"开始调用 initServices()..."}
{"level":"INFO","timestamp":"2025-07-01T22:56:46.666+0800","caller":"utils/logger.go:94","msg":"开始初始化服务..."}
{"level":"INFO","timestamp":"2025-07-01T22:56:46.671+0800","caller":"utils/logger.go:94","msg":"成功加载配置文件"}
{"level":"INFO","timestamp":"2025-07-01T22:56:46.674+0800","caller":"utils/logger.go:94","msg":"集成并发截图服务初始化成功"}
{"level":"INFO","timestamp":"2025-07-01T22:56:46.674+0800","caller":"utils/logger.go:94","msg":"开始初始化任务管理器..."}
{"level":"INFO","timestamp":"2025-07-01T22:56:46.674+0800","caller":"utils/logger.go:94","msg":"开始创建任务处理器..."}
{"level":"INFO","timestamp":"2025-07-01T22:56:46.674+0800","caller":"utils/logger.go:94","msg":"截图任务处理器创建完成"}
{"level":"INFO","timestamp":"2025-07-01T22:56:46.675+0800","caller":"utils/logger.go:94","msg":"OCR任务处理器创建完成"}
{"level":"INFO","timestamp":"2025-07-01T22:56:46.675+0800","caller":"utils/logger.go:94","msg":"上传任务处理器创建完成"}
{"level":"INFO","timestamp":"2025-07-01T22:56:46.675+0800","caller":"utils/logger.go:94","msg":"开始注册任务处理器..."}
{"level":"INFO","timestamp":"2025-07-01T22:56:46.675+0800","caller":"utils/logger.go:94","msg":"任务处理器注册成功","taskType":"screenshot_A"}
{"level":"INFO","timestamp":"2025-07-01T22:56:46.675+0800","caller":"utils/logger.go:94","msg":"截图A任务处理器注册成功"}
{"level":"INFO","timestamp":"2025-07-01T22:56:46.675+0800","caller":"utils/logger.go:94","msg":"任务处理器注册成功","taskType":"screenshot_B"}
{"level":"INFO","timestamp":"2025-07-01T22:56:46.675+0800","caller":"utils/logger.go:94","msg":"截图B任务处理器注册成功"}
{"level":"INFO","timestamp":"2025-07-01T22:56:46.675+0800","caller":"utils/logger.go:94","msg":"任务处理器注册成功","taskType":"screenshot_C"}
{"level":"INFO","timestamp":"2025-07-01T22:56:46.675+0800","caller":"utils/logger.go:94","msg":"截图C任务处理器注册成功"}
{"level":"INFO","timestamp":"2025-07-01T22:56:46.676+0800","caller":"utils/logger.go:94","msg":"任务处理器注册成功","taskType":"ocr_process"}
{"level":"INFO","timestamp":"2025-07-01T22:56:46.676+0800","caller":"utils/logger.go:94","msg":"OCR任务处理器注册成功"}
{"level":"INFO","timestamp":"2025-07-01T22:56:46.676+0800","caller":"utils/logger.go:94","msg":"任务处理器注册成功","taskType":"upload"}
{"level":"INFO","timestamp":"2025-07-01T22:56:46.676+0800","caller":"utils/logger.go:94","msg":"上传任务处理器注册成功"}
{"level":"INFO","timestamp":"2025-07-01T22:56:46.676+0800","caller":"utils/logger.go:94","msg":"开始启动任务管理器..."}
{"level":"INFO","timestamp":"2025-07-01T22:56:46.676+0800","caller":"utils/logger.go:94","msg":"任务管理器启动成功","maxConcurrent":3,"registeredHandlers":5,"cooldownPeriod":0.5}
{"level":"INFO","timestamp":"2025-07-01T22:56:46.676+0800","caller":"utils/logger.go:94","msg":"启动工作协程","workerCount":3}
{"level":"INFO","timestamp":"2025-07-01T22:56:46.676+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":0}
{"level":"INFO","timestamp":"2025-07-01T22:56:46.676+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":1}
{"level":"INFO","timestamp":"2025-07-01T22:56:46.677+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":2}
{"level":"INFO","timestamp":"2025-07-01T22:56:46.676+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":0}
{"level":"INFO","timestamp":"2025-07-01T22:56:46.677+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":1}
{"level":"INFO","timestamp":"2025-07-01T22:56:46.678+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":2}
{"level":"INFO","timestamp":"2025-07-01T22:56:46.678+0800","caller":"utils/logger.go:94","msg":"清理协程启动成功"}
{"level":"INFO","timestamp":"2025-07-01T22:56:46.679+0800","caller":"utils/logger.go:94","msg":"任务管理器启动事件已发送"}
{"level":"INFO","timestamp":"2025-07-01T22:56:46.679+0800","caller":"utils/logger.go:94","msg":"任务管理器启动成功"}
{"level":"INFO","timestamp":"2025-07-01T22:56:46.679+0800","caller":"utils/logger.go:94","msg":"任务管理器初始化成功"}
{"level":"INFO","timestamp":"2025-07-01T22:56:46.679+0800","caller":"utils/logger.go:94","msg":"开始从API加载站点信息..."}
{"level":"INFO","timestamp":"2025-07-01T22:56:47.279+0800","caller":"utils/logger.go:94","msg":"GetConfig called - SiteInfo: 北京市通州区潞城镇潞城社区卫生服务中心 (YL-BJ-TZ-001)"}
{"level":"INFO","timestamp":"2025-07-01T22:56:47.342+0800","caller":"utils/logger.go:94","msg":"DOM准备就绪，开始设置窗口位置和尺寸"}
{"level":"INFO","timestamp":"2025-07-01T22:56:47.351+0800","caller":"utils/logger.go:94","msg":"GetSiteInfo called - 开始获取站点信息"}
{"level":"INFO","timestamp":"2025-07-01T22:56:47.352+0800","caller":"utils/logger.go:94","msg":"使用缓存的站点信息"}
{"level":"INFO","timestamp":"2025-07-01T22:56:47.352+0800","caller":"utils/logger.go:94","msg":"GetSiteInfo - 成功获取站点信息: ID=YL-BJ-TZ-001, Name=北京市通州区潞城镇潞城社区卫生服务中心"}
{"level":"INFO","timestamp":"2025-07-01T22:56:47.365+0800","caller":"utils/logger.go:94","msg":"窗口位置设置为紧凑模式: x=2944, y=748, width=512, height=806"}
{"level":"INFO","timestamp":"2025-07-01T22:56:47.371+0800","caller":"utils/logger.go:94","msg":"窗体已设置为置顶"}
{"level":"INFO","timestamp":"2025-07-01T22:56:47.371+0800","caller":"utils/logger.go:94","msg":"窗体已显示"}
{"level":"INFO","timestamp":"2025-07-01T22:56:47.376+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T22:56:47.409+0800","caller":"utils/logger.go:94","msg":"站点信息无变化，复用现有二维码"}
{"level":"INFO","timestamp":"2025-07-01T22:56:47.409+0800","caller":"utils/logger.go:94","msg":"initServices() 完成"}
{"level":"INFO","timestamp":"2025-07-01T22:56:47.409+0800","caller":"utils/logger.go:94","msg":"开始创建并发截图服务..."}
{"level":"INFO","timestamp":"2025-07-01T22:56:47.409+0800","caller":"utils/logger.go:94","msg":"并发截图服务创建完成"}
{"level":"INFO","timestamp":"2025-07-01T22:56:47.409+0800","caller":"utils/logger.go:94","msg":"窗体状态初始化完成"}
{"level":"INFO","timestamp":"2025-07-01T22:56:47.409+0800","caller":"utils/logger.go:94","msg":"开始创建必要的目录..."}
{"level":"INFO","timestamp":"2025-07-01T22:56:47.410+0800","caller":"utils/logger.go:94","msg":"pic目录创建成功"}
{"level":"INFO","timestamp":"2025-07-01T22:56:47.410+0800","caller":"utils/logger.go:94","msg":"config目录创建成功"}
{"level":"INFO","timestamp":"2025-07-01T22:56:47.410+0800","caller":"utils/logger.go:94","msg":"开始启动快捷键监听..."}
{"level":"INFO","timestamp":"2025-07-01T22:56:47.410+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"启动快捷键监听","patient":"","site_id":""}
{"level":"INFO","timestamp":"2025-07-01T22:56:47.411+0800","caller":"utils/logger.go:94","msg":"快捷键服务启动成功"}
{"level":"INFO","timestamp":"2025-07-01T22:56:47.411+0800","caller":"utils/logger.go:94","msg":"启动OCR任务清理定时器..."}
{"level":"INFO","timestamp":"2025-07-01T22:56:47.411+0800","caller":"utils/logger.go:94","msg":"OCR任务清理定时器启动成功"}
{"level":"INFO","timestamp":"2025-07-01T22:56:47.412+0800","caller":"utils/logger.go:94","msg":"应用启动成功"}
{"level":"INFO","timestamp":"2025-07-01T22:56:47.987+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取待检测候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T22:56:47.987+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取指定日期已完成患者列表","patient":"2025-07-01","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T22:56:47.987+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取未分析患者列表","patient":"2025-07-01","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T22:56:48.641+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"生成报到二维码","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T22:56:48.641+0800","caller":"utils/logger.go:94","msg":"复用了缓存的报到二维码"}
{"level":"INFO","timestamp":"2025-07-01T22:56:48.648+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T22:56:48.893+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取待检测候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T22:56:49.065+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取指定日期已完成患者列表","patient":"2025-07-01","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T22:56:58.128+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"快捷键截图-模式B","patient":"","site_id":""}
{"level":"INFO","timestamp":"2025-07-01T22:56:58.128+0800","caller":"utils/logger.go:94","msg":"快捷键触发截图任务 - 模式: B"}
{"level":"INFO","timestamp":"2025-07-01T22:56:58.128+0800","caller":"utils/logger.go:94","msg":"收到截图任务请求: 快捷键B模式截图 (模式: B, 任务类型: screenshot_B)"}
{"level":"INFO","timestamp":"2025-07-01T22:56:58.128+0800","caller":"utils/logger.go:94","msg":"任务管理器可用，提交任务到队列"}
{"level":"INFO","timestamp":"2025-07-01T22:56:58.128+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T22:56:58.447+0800","caller":"utils/logger.go:94","msg":"收到任务提交请求","taskType":"screenshot_B","mode":"B","userName":"test-11200","roundNumber":0,"priority":"\u0002"}
{"level":"INFO","timestamp":"2025-07-01T22:56:58.447+0800","caller":"utils/logger.go:94","msg":"任务处理器检查通过","taskType":"screenshot_B"}
{"level":"INFO","timestamp":"2025-07-01T22:56:58.448+0800","caller":"utils/logger.go:94","msg":"任务创建成功","taskID":"screenshot_B_1751381818448177200_test-11200","taskType":"screenshot_B","mode":"B","userName":"test-11200","roundNumber":0,"timeout":35}
{"level":"INFO","timestamp":"2025-07-01T22:56:58.448+0800","caller":"utils/logger.go:94","msg":"任务提交成功","taskID":"screenshot_B_1751381818448177200_test-11200","taskType":"screenshot_B","userName":"test-11200","priority":2}
{"level":"INFO","timestamp":"2025-07-01T22:56:58.448+0800","caller":"utils/logger.go:94","msg":"开始处理任务","workerID":0,"taskID":"screenshot_B_1751381818448177200_test-11200","taskType":"screenshot_B","userName":"test-11200","mode":"B","roundNumber":0}
{"level":"INFO","timestamp":"2025-07-01T22:56:58.448+0800","caller":"utils/logger.go:94","msg":"成功提交快捷键B模式截图任务 - TaskID: screenshot_B_1751381818448177200_test-11200, User: test-11200, Round: 0"}
{"level":"INFO","timestamp":"2025-07-01T22:56:58.448+0800","caller":"utils/logger.go:94","msg":"开始处理任务","taskID":"screenshot_B_1751381818448177200_test-11200","taskType":"screenshot_B","workerID":0}
{"level":"INFO","timestamp":"2025-07-01T22:56:58.448+0800","caller":"utils/logger.go:94","msg":"截图任务已提交到任务管理器: 快捷键B模式截图"}
{"level":"INFO","timestamp":"2025-07-01T22:56:58.448+0800","caller":"utils/logger.go:94","msg":"执行任务处理器","taskID":"screenshot_B_1751381818448177200_test-11200","attempt":1}
{"level":"INFO","timestamp":"2025-07-01T22:56:58.448+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"执行截图任务-B","patient":"test-11200","site_id":""}
{"level":"INFO","timestamp":"2025-07-01T22:56:58.448+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T22:56:58.631+0800","caller":"utils/logger.go:94","msg":"[操作:快捷键截图-模式B] [当前受检者:test-11200] [网点:YL-BJ-TZ-001] [轮次:R01]"}
{"level":"INFO","timestamp":"2025-07-01T22:56:58.632+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取待检测候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T22:56:58.851+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"当前没有选中受检者，处于本系统操作者自行研究模式","patient":"暂无候检者","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T22:56:58.851+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"处理截图工作流程","patient":"test-11200","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T22:56:58.851+0800","caller":"utils/logger.go:94","msg":"开始处理截图工作流程 - 模式: B, 用户: test-11200\n"}
{"level":"INFO","timestamp":"2025-07-01T22:56:59.240+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T22:56:59.441+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"图片OCR识别","patient":"test-11200","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T22:57:02.372+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"快捷键截图-模式C","patient":"","site_id":""}
{"level":"INFO","timestamp":"2025-07-01T22:57:02.372+0800","caller":"utils/logger.go:94","msg":"快捷键触发截图任务 - 模式: C"}
{"level":"INFO","timestamp":"2025-07-01T22:57:02.372+0800","caller":"utils/logger.go:94","msg":"收到截图任务请求: 快捷键C模式截图 (模式: C, 任务类型: screenshot_C)"}
{"level":"INFO","timestamp":"2025-07-01T22:57:02.372+0800","caller":"utils/logger.go:94","msg":"任务管理器可用，提交任务到队列"}
{"level":"INFO","timestamp":"2025-07-01T22:57:02.372+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T22:57:02.547+0800","caller":"utils/logger.go:94","msg":"收到任务提交请求","taskType":"screenshot_C","mode":"C","userName":"test-11200","roundNumber":0,"priority":"\u0002"}
{"level":"INFO","timestamp":"2025-07-01T22:57:02.547+0800","caller":"utils/logger.go:94","msg":"任务处理器检查通过","taskType":"screenshot_C"}
{"level":"INFO","timestamp":"2025-07-01T22:57:02.547+0800","caller":"utils/logger.go:94","msg":"任务创建成功","taskID":"screenshot_C_1751381822547132900_test-11200","taskType":"screenshot_C","mode":"C","userName":"test-11200","roundNumber":0,"timeout":40}
{"level":"INFO","timestamp":"2025-07-01T22:57:02.547+0800","caller":"utils/logger.go:94","msg":"任务提交成功","taskID":"screenshot_C_1751381822547132900_test-11200","taskType":"screenshot_C","userName":"test-11200","priority":2}
{"level":"INFO","timestamp":"2025-07-01T22:57:02.547+0800","caller":"utils/logger.go:94","msg":"开始处理任务","workerID":1,"taskID":"screenshot_C_1751381822547132900_test-11200","taskType":"screenshot_C","userName":"test-11200","mode":"C","roundNumber":0}
{"level":"INFO","timestamp":"2025-07-01T22:57:02.547+0800","caller":"utils/logger.go:94","msg":"成功提交快捷键C模式截图任务 - TaskID: screenshot_C_1751381822547132900_test-11200, User: test-11200, Round: 0"}
{"level":"INFO","timestamp":"2025-07-01T22:57:02.547+0800","caller":"utils/logger.go:94","msg":"开始处理任务","taskID":"screenshot_C_1751381822547132900_test-11200","taskType":"screenshot_C","workerID":1}
{"level":"INFO","timestamp":"2025-07-01T22:57:02.548+0800","caller":"utils/logger.go:94","msg":"截图任务已提交到任务管理器: 快捷键C模式截图"}
{"level":"INFO","timestamp":"2025-07-01T22:57:02.548+0800","caller":"utils/logger.go:94","msg":"执行任务处理器","taskID":"screenshot_C_1751381822547132900_test-11200","attempt":1}
{"level":"INFO","timestamp":"2025-07-01T22:57:02.548+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"执行截图任务-C","patient":"test-11200","site_id":""}
{"level":"INFO","timestamp":"2025-07-01T22:57:02.548+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T22:57:02.768+0800","caller":"utils/logger.go:94","msg":"[操作:快捷键截图-模式C] [当前受检者:test-11200] [网点:YL-BJ-TZ-001] [轮次:R01]"}
{"level":"INFO","timestamp":"2025-07-01T22:57:02.768+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取待检测候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T22:57:02.954+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"当前没有选中受检者，处于本系统操作者自行研究模式","patient":"暂无候检者","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T22:57:02.954+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"处理截图工作流程","patient":"test-11200","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T22:57:02.954+0800","caller":"utils/logger.go:94","msg":"开始处理截图工作流程 - 模式: C, 用户: test-11200\n"}
{"level":"INFO","timestamp":"2025-07-01T22:57:03.357+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T22:57:03.544+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"图片OCR识别","patient":"test-11200","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T22:57:21.670+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"快捷键截图-模式B","patient":"","site_id":""}
{"level":"INFO","timestamp":"2025-07-01T22:57:21.670+0800","caller":"utils/logger.go:94","msg":"快捷键触发截图任务 - 模式: B"}
{"level":"INFO","timestamp":"2025-07-01T22:57:21.670+0800","caller":"utils/logger.go:94","msg":"收到截图任务请求: 快捷键B模式截图 (模式: B, 任务类型: screenshot_B)"}
{"level":"INFO","timestamp":"2025-07-01T22:57:21.670+0800","caller":"utils/logger.go:94","msg":"任务管理器可用，提交任务到队列"}
{"level":"INFO","timestamp":"2025-07-01T22:57:21.670+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T22:57:21.905+0800","caller":"utils/logger.go:94","msg":"收到任务提交请求","taskType":"screenshot_B","mode":"B","userName":"test-11200","roundNumber":0,"priority":"\u0002"}
{"level":"INFO","timestamp":"2025-07-01T22:57:21.905+0800","caller":"utils/logger.go:94","msg":"任务处理器检查通过","taskType":"screenshot_B"}
{"level":"ERROR","timestamp":"2025-07-01T22:57:21.905+0800","caller":"utils/logger.go:83","msg":"操作错误","operation":"提交快捷键B模式截图任务失败","patient":"test-11200","error":"task screenshot_B for user test-11200 round 0 is already running","stacktrace":"MagneticOperator/app/utils.LogError\n\tF:/myHbuilderAPP/MagneticOperator/app/utils/logger.go:83\nmain.(*App).SubmitScreenshotTask\n\tF:/myHbuilderAPP/MagneticOperator/app.go:430\nMagneticOperator/app/services.(*HotkeyService).handleScreenshot\n\tF:/myHbuilderAPP/MagneticOperator/app/services/hotkey_service.go:168\nMagneticOperator/app/services.(*HotkeyService).checkHotkeys\n\tF:/myHbuilderAPP/MagneticOperator/app/services/hotkey_service.go:132\nMagneticOperator/app/services.(*HotkeyService).StartHotkeyListener.func1\n\tF:/myHbuilderAPP/MagneticOperator/app/services/hotkey_service.go:81"}
{"level":"INFO","timestamp":"2025-07-01T22:57:21.905+0800","caller":"utils/logger.go:94","msg":"截图任务已提交到任务管理器: 快捷键B模式截图"}
{"level":"INFO","timestamp":"2025-07-01T22:57:27.892+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"快捷键截图-模式C","patient":"","site_id":""}
{"level":"INFO","timestamp":"2025-07-01T22:57:27.892+0800","caller":"utils/logger.go:94","msg":"快捷键触发截图任务 - 模式: C"}
{"level":"INFO","timestamp":"2025-07-01T22:57:27.892+0800","caller":"utils/logger.go:94","msg":"收到截图任务请求: 快捷键C模式截图 (模式: C, 任务类型: screenshot_C)"}
{"level":"INFO","timestamp":"2025-07-01T22:57:27.893+0800","caller":"utils/logger.go:94","msg":"任务管理器可用，提交任务到队列"}
{"level":"INFO","timestamp":"2025-07-01T22:57:27.893+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T22:57:28.251+0800","caller":"utils/logger.go:94","msg":"OCR API返回值详情 - 校验后器官名称: 腹部第1腰椎水平截面, 键值对数量: 40, 置信度: 0.00, 图片路径: pic\\temp\\temp_screenshot_B_test-11200_20250701_225659.png\n"}
{"level":"INFO","timestamp":"2025-07-01T22:57:28.538+0800","caller":"utils/logger.go:94","msg":"颜色检测成功完成","tempFilePath":"pic\\temp\\temp_screenshot_B_test-11200_20250701_225659.png"}
{"level":"INFO","timestamp":"2025-07-01T22:57:28.545+0800","caller":"utils/logger.go:94","msg":"开始第四步：提取D值列表数据","patientName":"test-11200","mode":"B"}
{"level":"INFO","timestamp":"2025-07-01T22:57:28.549+0800","caller":"utils/logger.go:94","msg":"提取D值列表成功","dataCount":0}
{"level":"INFO","timestamp":"2025-07-01T22:57:28.549+0800","caller":"utils/logger.go:94","msg":"开始更新用户检测信息","userName":"test-11200","mode":"B","imagePath":"pic\\temp\\temp_screenshot_B_test-11200_20250701_225659.png","organName":"腹部第1腰椎水平截面","operationID":"test-11200_B_1751381848549339000"}
{"level":"INFO","timestamp":"2025-07-01T22:57:28.553+0800","caller":"utils/logger.go:94","msg":"更新操作状态","status":"B02生化平衡分析已加入队列，等待C03病理形态学分析启动...","mode":"B","round":1,"completed":true}
{"level":"INFO","timestamp":"2025-07-01T22:57:28.553+0800","caller":"utils/logger.go:94","msg":"轮次数据更新","userName":"test-11200","currentRound":1,"mode":"B","organName":"腹部第1腰椎水平截面","oldCompletedRounds":0,"newCompletedRounds":0,"totalRounds":10,"roundsDataLength":1,"operationID":"test-11200_B_1751381848549339000"}
{"level":"INFO","timestamp":"2025-07-01T22:57:29.130+0800","caller":"utils/logger.go:94","msg":"收到任务提交请求","taskType":"screenshot_C","mode":"C","userName":"test-11200","roundNumber":0,"priority":"\u0002"}
{"level":"INFO","timestamp":"2025-07-01T22:57:29.130+0800","caller":"utils/logger.go:94","msg":"任务处理器检查通过","taskType":"screenshot_C"}
{"level":"ERROR","timestamp":"2025-07-01T22:57:29.130+0800","caller":"utils/logger.go:83","msg":"操作错误","operation":"提交快捷键C模式截图任务失败","patient":"test-11200","error":"task screenshot_C for user test-11200 round 0 is already running","stacktrace":"MagneticOperator/app/utils.LogError\n\tF:/myHbuilderAPP/MagneticOperator/app/utils/logger.go:83\nmain.(*App).SubmitScreenshotTask\n\tF:/myHbuilderAPP/MagneticOperator/app.go:430\nMagneticOperator/app/services.(*HotkeyService).handleScreenshot\n\tF:/myHbuilderAPP/MagneticOperator/app/services/hotkey_service.go:168\nMagneticOperator/app/services.(*HotkeyService).checkHotkeys\n\tF:/myHbuilderAPP/MagneticOperator/app/services/hotkey_service.go:132\nMagneticOperator/app/services.(*HotkeyService).StartHotkeyListener.func1\n\tF:/myHbuilderAPP/MagneticOperator/app/services/hotkey_service.go:81"}
{"level":"INFO","timestamp":"2025-07-01T22:57:29.131+0800","caller":"utils/logger.go:94","msg":"截图任务已提交到任务管理器: 快捷键C模式截图"}
{"level":"INFO","timestamp":"2025-07-01T22:57:55.187+0800","caller":"utils/logger.go:94","msg":"OCR API返回值详情 - 校验后器官名称: 腹部第1腰椎水平截面, 键值对数量: 1, 置信度: 0.00, 图片路径: pic\\temp\\temp_screenshot_C_test-11200_20250701_225703.png\n"}
{"level":"INFO","timestamp":"2025-07-01T22:57:55.187+0800","caller":"utils/logger.go:94","msg":"开始第四步：提取D值列表数据","patientName":"test-11200","mode":"C"}
{"level":"INFO","timestamp":"2025-07-01T22:57:55.190+0800","caller":"utils/logger.go:94","msg":"提取D值列表成功","dataCount":0}
{"level":"INFO","timestamp":"2025-07-01T22:57:55.190+0800","caller":"utils/logger.go:94","msg":"开始更新用户检测信息","userName":"test-11200","mode":"C","imagePath":"pic\\temp\\temp_screenshot_C_test-11200_20250701_225703.png","organName":"腹部第1腰椎水平截面","operationID":"test-11200_C_1751381875190448800"}
{"level":"INFO","timestamp":"2025-07-01T23:01:46.678+0800","caller":"utils/logger.go:94","msg":"清理已完成任务","remaining":0}
{"level":"INFO","timestamp":"2025-07-01T23:02:55.941+0800","caller":"utils/logger.go:94","msg":"应用程序启动","version":"1.0.0"}
{"level":"INFO","timestamp":"2025-07-01T23:02:55.941+0800","caller":"utils/logger.go:94","msg":"启动磁感分析操作台"}
{"level":"INFO","timestamp":"2025-07-01T23:02:55.941+0800","caller":"utils/logger.go:94","msg":"开始单实例检查"}
{"level":"INFO","timestamp":"2025-07-01T23:02:55.941+0800","caller":"utils/logger.go:94","msg":"检查现有实例..."}
{"level":"INFO","timestamp":"2025-07-01T23:02:55.941+0800","caller":"utils/logger.go:94","msg":"锁文件路径","path":"C:\\Users\\<USER>\\AppData\\Local\\Temp\\MagneticOperator.lock"}
{"level":"INFO","timestamp":"2025-07-01T23:02:55.941+0800","caller":"utils/logger.go:94","msg":"未找到现有锁文件"}
{"level":"INFO","timestamp":"2025-07-01T23:02:55.941+0800","caller":"utils/logger.go:94","msg":"尝试创建新锁文件..."}
{"level":"INFO","timestamp":"2025-07-01T23:02:55.941+0800","caller":"utils/logger.go:94","msg":"写入当前PID到锁文件","pid":49024}
{"level":"INFO","timestamp":"2025-07-01T23:02:55.945+0800","caller":"utils/logger.go:94","msg":"成功创建锁文件，允许启动"}
{"level":"INFO","timestamp":"2025-07-01T23:02:55.945+0800","caller":"utils/logger.go:94","msg":"单实例检查通过"}
{"level":"INFO","timestamp":"2025-07-01T23:02:55.946+0800","caller":"utils/logger.go:94","msg":"创建App实例..."}
{"level":"INFO","timestamp":"2025-07-01T23:02:55.946+0800","caller":"utils/logger.go:94","msg":"App实例创建成功"}
{"level":"INFO","timestamp":"2025-07-01T23:02:55.946+0800","caller":"utils/logger.go:94","msg":"开始启动Wails应用..."}
{"level":"INFO","timestamp":"2025-07-01T23:02:55.975+0800","caller":"utils/logger.go:94","msg":"Wails.Run()调用完成"}
{"level":"INFO","timestamp":"2025-07-01T23:02:55.975+0800","caller":"utils/logger.go:94","msg":"Wails应用正常退出"}
{"level":"INFO","timestamp":"2025-07-01T23:02:55.975+0800","caller":"utils/logger.go:94","msg":"清理锁文件..."}
{"level":"INFO","timestamp":"2025-07-01T23:02:55.975+0800","caller":"utils/logger.go:94","msg":"关闭锁文件句柄"}
{"level":"INFO","timestamp":"2025-07-01T23:02:55.975+0800","caller":"utils/logger.go:94","msg":"成功删除锁文件","path":"C:\\Users\\<USER>\\AppData\\Local\\Temp\\MagneticOperator.lock"}
{"level":"INFO","timestamp":"2025-07-01T23:02:56.087+0800","caller":"utils/logger.go:94","msg":"GetConfig called - SiteInfo: 北京市通州区潞城镇潞城社区卫生服务中心 (YL-BJ-TZ-001)"}
{"level":"INFO","timestamp":"2025-07-01T23:02:56.089+0800","caller":"utils/logger.go:94","msg":"GetConfig called - SiteInfo: 北京市通州区潞城镇潞城社区卫生服务中心 (YL-BJ-TZ-001)"}
{"level":"INFO","timestamp":"2025-07-01T23:02:56.091+0800","caller":"utils/logger.go:94","msg":"GetConfig called - SiteInfo: 北京市通州区潞城镇潞城社区卫生服务中心 (YL-BJ-TZ-001)"}
{"level":"INFO","timestamp":"2025-07-01T23:02:56.092+0800","caller":"utils/logger.go:94","msg":"GetSiteInfo called - 开始获取站点信息"}
{"level":"INFO","timestamp":"2025-07-01T23:02:56.092+0800","caller":"utils/logger.go:94","msg":"使用缓存的站点信息"}
{"level":"INFO","timestamp":"2025-07-01T23:02:56.092+0800","caller":"utils/logger.go:94","msg":"GetSiteInfo - 成功获取站点信息: ID=YL-BJ-TZ-001, Name=北京市通州区潞城镇潞城社区卫生服务中心"}
{"level":"INFO","timestamp":"2025-07-01T23:02:56.093+0800","caller":"utils/logger.go:94","msg":"GetSiteInfo called - 开始获取站点信息"}
{"level":"INFO","timestamp":"2025-07-01T23:02:56.093+0800","caller":"utils/logger.go:94","msg":"使用缓存的站点信息"}
{"level":"INFO","timestamp":"2025-07-01T23:02:56.093+0800","caller":"utils/logger.go:94","msg":"GetSiteInfo - 成功获取站点信息: ID=YL-BJ-TZ-001, Name=北京市通州区潞城镇潞城社区卫生服务中心"}
{"level":"INFO","timestamp":"2025-07-01T23:02:56.093+0800","caller":"utils/logger.go:94","msg":"GetSiteInfo called - 开始获取站点信息"}
{"level":"INFO","timestamp":"2025-07-01T23:02:56.093+0800","caller":"utils/logger.go:94","msg":"使用缓存的站点信息"}
{"level":"INFO","timestamp":"2025-07-01T23:02:56.093+0800","caller":"utils/logger.go:94","msg":"GetSiteInfo - 成功获取站点信息: ID=YL-BJ-TZ-001, Name=北京市通州区潞城镇潞城社区卫生服务中心"}
{"level":"INFO","timestamp":"2025-07-01T23:02:56.097+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T23:02:56.098+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T23:02:56.098+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T23:02:56.708+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取待检测候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T23:02:56.708+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取指定日期已完成患者列表","patient":"2025-07-01","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T23:02:56.708+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取未分析患者列表","patient":"2025-07-01","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T23:02:56.716+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取待检测候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T23:02:56.716+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取指定日期已完成患者列表","patient":"2025-07-01","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T23:02:56.716+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取未分析患者列表","patient":"2025-07-01","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T23:02:56.716+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取待检测候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T23:02:56.716+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取指定日期已完成患者列表","patient":"2025-07-01","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T23:02:56.716+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取未分析患者列表","patient":"2025-07-01","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T23:02:57.322+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"生成报到二维码","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T23:02:57.322+0800","caller":"utils/logger.go:94","msg":"复用了缓存的报到二维码"}
{"level":"INFO","timestamp":"2025-07-01T23:02:57.323+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T23:02:57.326+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"生成报到二维码","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T23:02:57.327+0800","caller":"utils/logger.go:94","msg":"复用了缓存的报到二维码"}
{"level":"INFO","timestamp":"2025-07-01T23:02:57.327+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"生成报到二维码","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T23:02:57.327+0800","caller":"utils/logger.go:94","msg":"复用了缓存的报到二维码"}
{"level":"INFO","timestamp":"2025-07-01T23:02:57.329+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T23:02:57.330+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T23:02:57.507+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取待检测候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T23:02:57.548+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取待检测候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T23:02:57.552+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取待检测候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T23:02:57.693+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取指定日期已完成患者列表","patient":"2025-07-01","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T23:02:57.796+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取指定日期已完成患者列表","patient":"2025-07-01","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T23:02:57.797+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取指定日期已完成患者列表","patient":"2025-07-01","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T23:02:58.942+0800","caller":"utils/logger.go:94","msg":"应用程序启动","version":"1.0.0"}
{"level":"INFO","timestamp":"2025-07-01T23:02:58.943+0800","caller":"utils/logger.go:94","msg":"启动磁感分析操作台"}
{"level":"INFO","timestamp":"2025-07-01T23:02:58.943+0800","caller":"utils/logger.go:94","msg":"开始单实例检查"}
{"level":"INFO","timestamp":"2025-07-01T23:02:58.943+0800","caller":"utils/logger.go:94","msg":"检查现有实例..."}
{"level":"INFO","timestamp":"2025-07-01T23:02:58.943+0800","caller":"utils/logger.go:94","msg":"锁文件路径","path":"F:\\myHbuilderAPP\\MagneticOperator\\build\\bin\\MagneticOperator.lock"}
{"level":"INFO","timestamp":"2025-07-01T23:02:58.943+0800","caller":"utils/logger.go:94","msg":"发现现有锁文件","size":5,"modified":"2025-07-01T22:56:45.422+0800"}
{"level":"INFO","timestamp":"2025-07-01T23:02:58.943+0800","caller":"utils/logger.go:94","msg":"锁文件内容","pid":"23160"}
{"level":"INFO","timestamp":"2025-07-01T23:02:58.943+0800","caller":"utils/logger.go:94","msg":"检查进程是否运行","pid":23160}
{"level":"INFO","timestamp":"2025-07-01T23:02:58.943+0800","caller":"utils/logger.go:94","msg":"检查进程是否运行","pid":23160}
{"level":"WARN","timestamp":"2025-07-01T23:02:58.943+0800","caller":"utils/logger.go:101","msg":"查找进程失败","pid":23160,"error":"OpenProcess: The parameter is incorrect."}
{"level":"INFO","timestamp":"2025-07-01T23:02:58.944+0800","caller":"utils/logger.go:94","msg":"进程未找到，清理旧锁文件","pid":23160}
{"level":"INFO","timestamp":"2025-07-01T23:02:58.944+0800","caller":"utils/logger.go:94","msg":"成功删除旧锁文件"}
{"level":"INFO","timestamp":"2025-07-01T23:02:58.944+0800","caller":"utils/logger.go:94","msg":"尝试创建新锁文件..."}
{"level":"INFO","timestamp":"2025-07-01T23:02:58.944+0800","caller":"utils/logger.go:94","msg":"写入当前PID到锁文件","pid":46956}
{"level":"INFO","timestamp":"2025-07-01T23:02:59.017+0800","caller":"utils/logger.go:94","msg":"成功创建锁文件，允许启动"}
{"level":"INFO","timestamp":"2025-07-01T23:02:59.018+0800","caller":"utils/logger.go:94","msg":"单实例检查通过"}
{"level":"INFO","timestamp":"2025-07-01T23:02:59.018+0800","caller":"utils/logger.go:94","msg":"创建App实例..."}
{"level":"INFO","timestamp":"2025-07-01T23:02:59.018+0800","caller":"utils/logger.go:94","msg":"App实例创建成功"}
{"level":"INFO","timestamp":"2025-07-01T23:02:59.018+0800","caller":"utils/logger.go:94","msg":"开始启动Wails应用..."}
{"level":"INFO","timestamp":"2025-07-01T23:02:59.465+0800","caller":"utils/logger.go:94","msg":"=== STARTUP函数被调用 ==="}
{"level":"INFO","timestamp":"2025-07-01T23:02:59.465+0800","caller":"utils/logger.go:94","msg":"日志系统初始化成功"}
{"level":"INFO","timestamp":"2025-07-01T23:02:59.466+0800","caller":"utils/logger.go:94","msg":"消息配置系统初始化成功"}
{"level":"INFO","timestamp":"2025-07-01T23:02:59.466+0800","caller":"utils/logger.go:94","msg":"开始初始化服务..."}
{"level":"INFO","timestamp":"2025-07-01T23:02:59.466+0800","caller":"utils/logger.go:94","msg":"开始调用 initServices()..."}
{"level":"INFO","timestamp":"2025-07-01T23:02:59.466+0800","caller":"utils/logger.go:94","msg":"开始初始化服务..."}
{"level":"INFO","timestamp":"2025-07-01T23:02:59.472+0800","caller":"utils/logger.go:94","msg":"成功加载配置文件"}
{"level":"INFO","timestamp":"2025-07-01T23:02:59.474+0800","caller":"utils/logger.go:94","msg":"集成并发截图服务初始化成功"}
{"level":"INFO","timestamp":"2025-07-01T23:02:59.474+0800","caller":"utils/logger.go:94","msg":"开始初始化任务管理器..."}
{"level":"INFO","timestamp":"2025-07-01T23:02:59.474+0800","caller":"utils/logger.go:94","msg":"开始创建任务处理器..."}
{"level":"INFO","timestamp":"2025-07-01T23:02:59.474+0800","caller":"utils/logger.go:94","msg":"截图任务处理器创建完成"}
{"level":"INFO","timestamp":"2025-07-01T23:02:59.474+0800","caller":"utils/logger.go:94","msg":"OCR任务处理器创建完成"}
{"level":"INFO","timestamp":"2025-07-01T23:02:59.474+0800","caller":"utils/logger.go:94","msg":"上传任务处理器创建完成"}
{"level":"INFO","timestamp":"2025-07-01T23:02:59.474+0800","caller":"utils/logger.go:94","msg":"开始注册任务处理器..."}
{"level":"INFO","timestamp":"2025-07-01T23:02:59.474+0800","caller":"utils/logger.go:94","msg":"任务处理器注册成功","taskType":"screenshot_A"}
{"level":"INFO","timestamp":"2025-07-01T23:02:59.475+0800","caller":"utils/logger.go:94","msg":"截图A任务处理器注册成功"}
{"level":"INFO","timestamp":"2025-07-01T23:02:59.475+0800","caller":"utils/logger.go:94","msg":"任务处理器注册成功","taskType":"screenshot_B"}
{"level":"INFO","timestamp":"2025-07-01T23:02:59.475+0800","caller":"utils/logger.go:94","msg":"截图B任务处理器注册成功"}
{"level":"INFO","timestamp":"2025-07-01T23:02:59.475+0800","caller":"utils/logger.go:94","msg":"任务处理器注册成功","taskType":"screenshot_C"}
{"level":"INFO","timestamp":"2025-07-01T23:02:59.475+0800","caller":"utils/logger.go:94","msg":"截图C任务处理器注册成功"}
{"level":"INFO","timestamp":"2025-07-01T23:02:59.476+0800","caller":"utils/logger.go:94","msg":"任务处理器注册成功","taskType":"ocr_process"}
{"level":"INFO","timestamp":"2025-07-01T23:02:59.476+0800","caller":"utils/logger.go:94","msg":"OCR任务处理器注册成功"}
{"level":"INFO","timestamp":"2025-07-01T23:02:59.476+0800","caller":"utils/logger.go:94","msg":"任务处理器注册成功","taskType":"upload"}
{"level":"INFO","timestamp":"2025-07-01T23:02:59.476+0800","caller":"utils/logger.go:94","msg":"上传任务处理器注册成功"}
{"level":"INFO","timestamp":"2025-07-01T23:02:59.476+0800","caller":"utils/logger.go:94","msg":"开始启动任务管理器..."}
{"level":"INFO","timestamp":"2025-07-01T23:02:59.476+0800","caller":"utils/logger.go:94","msg":"任务管理器启动成功","maxConcurrent":3,"registeredHandlers":5,"cooldownPeriod":0.5}
{"level":"INFO","timestamp":"2025-07-01T23:02:59.476+0800","caller":"utils/logger.go:94","msg":"启动工作协程","workerCount":3}
{"level":"INFO","timestamp":"2025-07-01T23:02:59.476+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":0}
{"level":"INFO","timestamp":"2025-07-01T23:02:59.476+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":0}
{"level":"INFO","timestamp":"2025-07-01T23:02:59.476+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":1}
{"level":"INFO","timestamp":"2025-07-01T23:02:59.476+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":1}
{"level":"INFO","timestamp":"2025-07-01T23:02:59.477+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":2}
{"level":"INFO","timestamp":"2025-07-01T23:02:59.477+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":2}
{"level":"INFO","timestamp":"2025-07-01T23:02:59.477+0800","caller":"utils/logger.go:94","msg":"清理协程启动成功"}
{"level":"INFO","timestamp":"2025-07-01T23:02:59.477+0800","caller":"utils/logger.go:94","msg":"任务管理器启动事件已发送"}
{"level":"INFO","timestamp":"2025-07-01T23:02:59.477+0800","caller":"utils/logger.go:94","msg":"任务管理器启动成功"}
{"level":"INFO","timestamp":"2025-07-01T23:02:59.477+0800","caller":"utils/logger.go:94","msg":"任务管理器初始化成功"}
{"level":"INFO","timestamp":"2025-07-01T23:02:59.478+0800","caller":"utils/logger.go:94","msg":"开始从API加载站点信息..."}
{"level":"INFO","timestamp":"2025-07-01T23:02:59.685+0800","caller":"utils/logger.go:94","msg":"GetConfig called - SiteInfo: 北京市通州区潞城镇潞城社区卫生服务中心 (YL-BJ-TZ-001)"}
{"level":"INFO","timestamp":"2025-07-01T23:02:59.752+0800","caller":"utils/logger.go:94","msg":"DOM准备就绪，开始设置窗口位置和尺寸"}
{"level":"INFO","timestamp":"2025-07-01T23:02:59.762+0800","caller":"utils/logger.go:94","msg":"GetSiteInfo called - 开始获取站点信息"}
{"level":"INFO","timestamp":"2025-07-01T23:02:59.762+0800","caller":"utils/logger.go:94","msg":"使用缓存的站点信息"}
{"level":"INFO","timestamp":"2025-07-01T23:02:59.762+0800","caller":"utils/logger.go:94","msg":"GetSiteInfo - 成功获取站点信息: ID=YL-BJ-TZ-001, Name=北京市通州区潞城镇潞城社区卫生服务中心"}
{"level":"INFO","timestamp":"2025-07-01T23:02:59.773+0800","caller":"utils/logger.go:94","msg":"窗口位置设置为紧凑模式: x=2944, y=748, width=512, height=806"}
{"level":"INFO","timestamp":"2025-07-01T23:02:59.777+0800","caller":"utils/logger.go:94","msg":"窗体已设置为置顶"}
{"level":"INFO","timestamp":"2025-07-01T23:02:59.777+0800","caller":"utils/logger.go:94","msg":"窗体已显示"}
{"level":"INFO","timestamp":"2025-07-01T23:02:59.789+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T23:02:59.957+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取待检测候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T23:02:59.957+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取指定日期已完成患者列表","patient":"2025-07-01","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T23:02:59.957+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取未分析患者列表","patient":"2025-07-01","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T23:03:00.115+0800","caller":"utils/logger.go:94","msg":"站点信息无变化，复用现有二维码"}
{"level":"INFO","timestamp":"2025-07-01T23:03:00.115+0800","caller":"utils/logger.go:94","msg":"initServices() 完成"}
{"level":"INFO","timestamp":"2025-07-01T23:03:00.116+0800","caller":"utils/logger.go:94","msg":"开始创建并发截图服务..."}
{"level":"INFO","timestamp":"2025-07-01T23:03:00.116+0800","caller":"utils/logger.go:94","msg":"并发截图服务创建完成"}
{"level":"INFO","timestamp":"2025-07-01T23:03:00.116+0800","caller":"utils/logger.go:94","msg":"窗体状态初始化完成"}
{"level":"INFO","timestamp":"2025-07-01T23:03:00.116+0800","caller":"utils/logger.go:94","msg":"开始创建必要的目录..."}
{"level":"INFO","timestamp":"2025-07-01T23:03:00.116+0800","caller":"utils/logger.go:94","msg":"pic目录创建成功"}
{"level":"INFO","timestamp":"2025-07-01T23:03:00.116+0800","caller":"utils/logger.go:94","msg":"config目录创建成功"}
{"level":"INFO","timestamp":"2025-07-01T23:03:00.116+0800","caller":"utils/logger.go:94","msg":"开始启动快捷键监听..."}
{"level":"INFO","timestamp":"2025-07-01T23:03:00.116+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"启动快捷键监听","patient":"","site_id":""}
{"level":"INFO","timestamp":"2025-07-01T23:03:00.116+0800","caller":"utils/logger.go:94","msg":"快捷键服务启动成功"}
{"level":"INFO","timestamp":"2025-07-01T23:03:00.117+0800","caller":"utils/logger.go:94","msg":"启动OCR任务清理定时器..."}
{"level":"INFO","timestamp":"2025-07-01T23:03:00.117+0800","caller":"utils/logger.go:94","msg":"OCR任务清理定时器启动成功"}
{"level":"INFO","timestamp":"2025-07-01T23:03:00.117+0800","caller":"utils/logger.go:94","msg":"应用启动成功"}
{"level":"INFO","timestamp":"2025-07-01T23:03:00.242+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"生成报到二维码","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T23:03:00.242+0800","caller":"utils/logger.go:94","msg":"复用了缓存的报到二维码"}
{"level":"INFO","timestamp":"2025-07-01T23:03:00.248+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T23:03:00.450+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取待检测候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-01T23:03:00.633+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取指定日期已完成患者列表","patient":"2025-07-01","site_id":"YL-BJ-TZ-001"}
