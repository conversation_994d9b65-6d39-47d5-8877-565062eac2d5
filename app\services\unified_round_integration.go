package services

import (
	"fmt"
	"strings"
	"time"

	"MagneticOperator/app/models"
	"MagneticOperator/app/utils"
	"go.uber.org/zap"
)

// UnifiedRoundIntegration 统一轮次管理集成器
type UnifiedRoundIntegration struct {
	manager *UnifiedRoundManager
	app     *App // 引用主应用
}

// NewUnifiedRoundIntegration 创建统一轮次管理集成器
func NewUnifiedRoundIntegration(app *App) *UnifiedRoundIntegration {
	// 创建统一轮次管理器
	manager := NewUnifiedRoundManager()
	
	integration := &UnifiedRoundIntegration{
		manager: manager,
		app:     app,
	}
	
	// 注册事件处理器
	integration.registerEventHandlers()
	
	return integration
}

// Start 启动统一轮次管理
func (uri *UnifiedRoundIntegration) Start() error {
	return uri.manager.Start()
}

// Stop 停止统一轮次管理
func (uri *UnifiedRoundIntegration) Stop() error {
	return uri.manager.Stop()
}

// GetManager 获取统一轮次管理器
func (uri *UnifiedRoundIntegration) GetManager() *UnifiedRoundManager {
	return uri.manager
}

// registerEventHandlers 注册事件处理器
func (uri *UnifiedRoundIntegration) registerEventHandlers() {
	// 注册轮次开始事件处理器
	uri.manager.Subscribe(EventRoundStart, uri.handleRoundStart)
	
	// 注册轮次完成事件处理器
	uri.manager.Subscribe(EventRoundComplete, uri.handleRoundComplete)
	
	// 注册模式完成事件处理器
	uri.manager.Subscribe(EventModeComplete, uri.handleModeComplete)
	
	// 注册所有轮次完成事件处理器
	uri.manager.Subscribe(EventAllRoundsComplete, uri.handleAllRoundsComplete)
	
	// 注册数据同步事件处理器
	uri.manager.Subscribe(EventDataSync, uri.handleDataSync)
}

// handleRoundStart 处理轮次开始事件
func (uri *UnifiedRoundIntegration) handleRoundStart(event RoundEvent) error {
	utils.LogInfo("轮次开始",
		zap.String("user_key", event.UserKey),
		zap.Int("round", event.Round),
	)
	
	// 发送Toast通知
	if uri.app != nil {
		toastData := ToastNotificationData{
			Message: fmt.Sprintf("开始第 %d 轮检测", event.Round),
			Type:    "info",
		}
		uri.app.SendToastNotification(toastData)
	}
	
	return nil
}

// handleRoundComplete 处理轮次完成事件
func (uri *UnifiedRoundIntegration) handleRoundComplete(event RoundEvent) error {
	utils.LogInfo("轮次完成",
		zap.String("user_key", event.UserKey),
		zap.Int("round", event.Round),
	)
	
	// 发送Toast通知
	if uri.app != nil {
		toastData := ToastNotificationData{
			Message: fmt.Sprintf("第 %d 轮检测完成", event.Round),
			Type:    "success",
		}
		uri.app.SendToastNotification(toastData)
		
		// 更新进度条
		progress := float64(event.Round) / float64(uri.manager.maxRounds) * 100
		uri.app.UpdateProgress(progress)
	}
	
	return nil
}

// handleModeComplete 处理模式完成事件
func (uri *UnifiedRoundIntegration) handleModeComplete(event RoundEvent) error {
	mode, ok := event.Data.(string)
	if !ok {
		return fmt.Errorf("模式数据类型错误")
	}
	
	utils.LogInfo("模式完成",
		zap.String("user_key", event.UserKey),
		zap.Int("round", event.Round),
		zap.String("mode", mode),
	)
	
	// 发送Toast通知
	if uri.app != nil {
		toastData := ToastNotificationData{
			Message: fmt.Sprintf("第 %d 轮 %s 模式完成", event.Round, mode),
			Type:    "info",
		}
		uri.app.SendToastNotification(toastData)
	}
	
	return nil
}

// handleAllRoundsComplete 处理所有轮次完成事件
func (uri *UnifiedRoundIntegration) handleAllRoundsComplete(event RoundEvent) error {
	utils.LogInfo("所有轮次完成",
		zap.String("user_key", event.UserKey),
		zap.Int("total_rounds", event.Round),
	)
	
	// 发送Toast通知
	if uri.app != nil {
		toastData := ToastNotificationData{
			Message: "恭喜！所有10轮检测已完成",
			Type:    "success",
		}
		uri.app.SendToastNotification(toastData)
		
		// 更新进度条到100%
		uri.app.UpdateProgress(100.0)
	}
	
	return nil
}

// handleDataSync 处理数据同步事件
func (uri *UnifiedRoundIntegration) handleDataSync(event RoundEvent) error {
	userData, ok := event.Data.(*models.CurrentUserCheckingInfo)
	if !ok {
		return fmt.Errorf("用户数据类型错误")
	}
	
	utils.LogDebug("数据同步",
		zap.String("user_key", event.UserKey),
		zap.Int("completed_rounds", userData.CompletedRounds),
		zap.Int("total_screenshots", userData.TotalScreenshots),
	)
	
	// 同步到原有的数据结构
	if uri.app != nil {
		uri.app.currentUserCheckingInfoMutex.Lock()
		uri.app.currentUserCheckingInfo[event.UserKey] = userData
		uri.app.currentUserCheckingInfoMutex.Unlock()
	}
	
	return nil
}

// MigrateFromLegacyRoundManager 从旧的轮次管理器迁移数据
func (uri *UnifiedRoundIntegration) MigrateFromLegacyRoundManager() error {
	if uri.app == nil {
		return fmt.Errorf("应用引用为空")
	}
	
	// 迁移 roundManager 数据
	for userKey, roundStatus := range uri.app.roundManager {
		// 创建统一轮次状态
		unifiedStatus := &UnifiedRoundStatus{
			UserName:        extractUserNameFromKey(userKey),
			CurrentRound:    roundStatus.CurrentRound,
			B02Completed:    roundStatus.B02Completed,
			C03Completed:    roundStatus.C03Completed,
			Status:          StatusInProgress,
			StartTime:       time.Now(),
			LastUpdateTime:  time.Now(),
			TotalScreenshots: 0,
			ProcessedOCRs:   0,
		}
		
		// 添加到统一管理器
		uri.manager.globalMutex.Lock()
		uri.manager.rounds[userKey] = unifiedStatus
		uri.manager.globalMutex.Unlock()
		
		utils.LogInfo("迁移轮次数据",
			zap.String("user_key", userKey),
			zap.Int("current_round", roundStatus.CurrentRound),
			zap.Bool("b02_completed", roundStatus.B02Completed),
			zap.Bool("c03_completed", roundStatus.C03Completed),
		)
	}
	
	// 迁移 currentUserCheckingInfo 数据
	uri.app.currentUserCheckingInfoMutex.RLock()
	for userKey, userInfo := range uri.app.currentUserCheckingInfo {
		uri.manager.dataManager.SetUserData(userKey, userInfo)
		
		utils.LogInfo("迁移用户检测数据",
			zap.String("user_key", userKey),
			zap.Int("completed_rounds", userInfo.CompletedRounds),
			zap.Int("total_screenshots", userInfo.TotalScreenshots),
		)
	}
	uri.app.currentUserCheckingInfoMutex.RUnlock()
	
	return nil
}

// extractUserNameFromKey 从用户键中提取用户名
func extractUserNameFromKey(userKey string) string {
	// 假设用户键格式为 "username_date"
	// 这里需要根据实际的键格式进行调整
	parts := strings.Split(userKey, "_")
	if len(parts) > 0 {
		return parts[0]
	}
	return userKey
}

// GetLegacyCompatibleMethods 获取兼容旧接口的方法
func (uri *UnifiedRoundIntegration) GetLegacyCompatibleMethods() *LegacyCompatibleMethods {
	return &LegacyCompatibleMethods{
		manager: uri.manager,
	}
}

// LegacyCompatibleMethods 兼容旧接口的方法集合
type LegacyCompatibleMethods struct {
	manager *UnifiedRoundManager
}

// MarkModeCompleted 兼容旧的 markModeCompleted 方法
func (lcm *LegacyCompatibleMethods) MarkModeCompleted(userName, mode string) (int, bool, int) {
	return lcm.manager.MarkModeCompleted(userName, mode)
}

// GetCurrentRound 兼容旧的 getCurrentRound 方法
func (lcm *LegacyCompatibleMethods) GetCurrentRound(userName string) int {
	return lcm.manager.GetCurrentRound(userName)
}

// GetCurrentRoundNumber 兼容旧的 GetCurrentRoundNumber 方法
func (lcm *LegacyCompatibleMethods) GetCurrentRoundNumber(userName string) int {
	return lcm.manager.GetCurrentRound(userName)
}

// UpdateCurrentUserCheckingInfo 兼容旧的 updateCurrentUserCheckingInfo 方法
func (lcm *LegacyCompatibleMethods) UpdateCurrentUserCheckingInfo(userKey string, updateFunc func(*models.CurrentUserCheckingInfo)) error {
	return lcm.manager.dataManager.UpdateUserData(userKey, updateFunc)
}